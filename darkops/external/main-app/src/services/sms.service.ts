"use client";

import apiClient from "@/lib/api/axios";

/**
 * Individual model result interface for dual-model system
 */
export interface ModelResult {
  is_phishing: boolean;
  confidence: number;
  risk_level: string;
  model_name: string;
  explanation?: string;
  status: string;
}

/**
 * Combined assessment interface
 */
export interface CombinedAssessment {
  is_phishing: boolean;
  confidence: number;
  risk_level: string;
  consensus: boolean;
  explanation: string;
}

/**
 * Enhanced SMS Analysis Response interface for dual-model system
 */
export interface SMSAnalysisResponse {
  // Legacy fields for backward compatibility
  prediction: string;
  is_phishing: boolean;
  confidence?: number;
  risk_level?: string;

  // Dual-model results
  model_1?: ModelResult;
  model_2?: ModelResult;

  // Combined assessment
  combined_assessment?: CombinedAssessment;

  // Analysis metadata
  analysis_timestamp?: string;
  models_used?: string[];

  // Legacy scan engines (for backward compatibility)
  scanEngines?: {
    name: string;
    result: string;
    confidence: number;
    risk_level: string;
    details: string;
    updateDate: string;
    version: string;
  }[];
}

/**
 * SMS Analysis Service for interacting with the SMS analysis API
 */
export const SMSAnalysisService = {
  /**
   * Analyze SMS message for phishing
   * @param message SMS message to analyze
   * @returns Analysis results
   */
  analyzeSMS: async (message: string): Promise<SMSAnalysisResponse> => {
    try {
      const response = await apiClient.post<SMSAnalysisResponse>(
        "/ai/sms/detect-phishing",
        { message },
      );
      return response.data;
    } catch (error) {
      console.error("Error analyzing SMS:", error);
      throw error;
    }
  },
};

export default SMSAnalysisService;
