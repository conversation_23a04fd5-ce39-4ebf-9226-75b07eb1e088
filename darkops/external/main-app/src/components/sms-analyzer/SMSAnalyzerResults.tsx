"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import { SMSAnalysisResponse } from "@/services/sms.service";

interface SMSAnalyzerResultsProps {
  results: SMSAnalysisResponse;
  smsText: string;
  onAnalyzeAgain: () => void;
}

export default function SMSAnalyzerResults({
  results,
  smsText,
  onAnalyzeAgain,
}: SMSAnalyzerResultsProps) {
  const [activeTab, setActiveTab] = useState<"overview" | "details">(
    "overview",
  );

  const formatConfidence = (confidence: number) => {
    // Check if confidence is already in percentage format (greater than 1)
    if (confidence > 1) {
      return confidence.toFixed(2) + "%";
    }
    // Otherwise, convert from decimal to percentage
    return (confidence * 100).toFixed(2) + "%";
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case "high":
        return "text-status-danger";
      case "medium":
        return "text-status-warning";
      case "low":
        return "text-status-safe";
      default:
        return "text-gray-600";
    }
  };

  const getModelDisplayName = (modelName: string) => {
    if (modelName.includes("Gemini") || modelName.includes("Model 2")) {
      return "Model V2.0";
    }
    if (modelName.includes("BERT") || modelName.includes("Model 1")) {
      return "Model V1.0";
    }
    return modelName;
  };

  const getConsensusIcon = (consensus: boolean) => {
    return consensus ? (
      <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clipRule="evenodd"
          />
        </svg>
        <span className="text-sm font-medium">Models Agree</span>
      </div>
    ) : (
      <div className="flex items-center gap-1 text-amber-600 dark:text-amber-400">
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
        <span className="text-sm font-medium">Models Disagree</span>
      </div>
    );
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-gradient-to-b from-purple-50/70 to-white p-6 dark:border-gray-700 dark:from-purple-950/10 dark:to-gray-900">
      <div className="mb-6 flex items-center gap-3">
        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100/80 dark:bg-purple-900/20">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-purple-600 dark:text-purple-400"
          >
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
          </svg>
        </div>
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          SMS Analysis Results
        </h1>
      </div>

      <p className="mb-6 text-gray-600 dark:text-gray-300">
        Phishing detection for the analyzed SMS
      </p>

      {/* Tabs */}
      <div className="mb-6 flex border-b border-gray-200 dark:border-gray-700">
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === "overview"
              ? "border-b-2 border-purple-600 text-purple-600 dark:border-purple-400 dark:text-purple-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          }`}
          onClick={() => setActiveTab("overview")}
        >
          Overview
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === "details"
              ? "border-b-2 border-purple-600 text-purple-600 dark:border-purple-400 dark:text-purple-400"
              : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          }`}
          onClick={() => setActiveTab("details")}
        >
          Details
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="mb-6"
        >
          <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
              Message Content
            </h3>
            <p className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">
              {smsText}
            </p>
          </div>

          <div className="mb-6">
            <div
              className={`rounded-lg p-4 ${
                results.is_phishing
                  ? "bg-status-danger/10 border-status-danger/30 border"
                  : "bg-status-safe/10 border-status-safe/30 border"
              }`}
            >
              <div className="flex items-center gap-2">
                {results.is_phishing ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-status-danger"
                  >
                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
                    <line x1="12" y1="9" x2="12" y2="13" />
                    <line x1="12" y1="17" x2="12.01" y2="17" />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-status-safe"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                    <polyline points="22 4 12 14.01 9 11.01" />
                  </svg>
                )}
                <h3
                  className={`text-lg font-medium ${
                    results.is_phishing
                      ? "text-status-danger"
                      : "text-status-safe"
                  }`}
                >
                  {results.is_phishing
                    ? "Phishing Detected"
                    : "No Phishing Detected"}
                </h3>
              </div>
              <p
                className={`mt-2 ${
                  results.is_phishing
                    ? "text-status-danger/80"
                    : "text-status-safe/80"
                }`}
              >
                {results.is_phishing
                  ? "This message has been identified as a potential phishing attempt with high confidence."
                  : "This message appears to be legitimate and does not contain phishing indicators."}
              </p>
            </div>
          </div>

          {/* Combined Assessment Section */}
          {results.combined_assessment && (
            <div className="mb-6">
              <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
                Combined Assessment
              </h3>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <div className="mb-3 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <h4 className="text-base font-medium text-gray-900 dark:text-white">
                      Final Result
                    </h4>
                    {getConsensusIcon(results.combined_assessment.consensus)}
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div>
                    <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                      Confidence
                    </p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {formatConfidence(results.combined_assessment.confidence)}
                    </p>
                  </div>
                  <div>
                    <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                      Risk Level
                    </p>
                    <p
                      className={`text-lg font-semibold ${getRiskLevelColor(
                        results.combined_assessment.risk_level,
                      )}`}
                    >
                      {results.combined_assessment.risk_level
                        .charAt(0)
                        .toUpperCase() +
                        results.combined_assessment.risk_level.slice(1)}
                    </p>
                  </div>
                  <div>
                    <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                      Models Used
                    </p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {results.models_used?.length || 1}
                    </p>
                  </div>
                </div>
                {results.combined_assessment.explanation && (
                  <div className="mt-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {results.combined_assessment.explanation}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Individual Model Results */}
          {(results.model_1 || results.model_2) && (
            <div className="mb-6">
              <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
                Individual Model Results
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {results.model_1 && (
                  <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                    <div className="mb-3 flex items-center justify-between">
                      <h4 className="text-base font-medium text-gray-900 dark:text-white">
                        {getModelDisplayName(results.model_1.model_name)}
                      </h4>
                      <span
                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                          results.model_1.is_phishing
                            ? "bg-status-danger/10 text-status-danger"
                            : "bg-status-safe/10 text-status-safe"
                        }`}
                      >
                        {results.model_1.is_phishing ? "Phishing" : "Safe"}
                      </span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          Confidence:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatConfidence(results.model_1.confidence)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          Risk Level:
                        </span>
                        <span
                          className={`text-sm font-medium ${getRiskLevelColor(
                            results.model_1.risk_level,
                          )}`}
                        >
                          {results.model_1.risk_level.charAt(0).toUpperCase() +
                            results.model_1.risk_level.slice(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
                {results.model_2 && (
                  <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                    <div className="mb-3 flex items-center justify-between">
                      <h4 className="text-base font-medium text-gray-900 dark:text-white">
                        {getModelDisplayName(results.model_2.model_name)}
                      </h4>
                      <span
                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                          results.model_2.is_phishing
                            ? "bg-status-danger/10 text-status-danger"
                            : "bg-status-safe/10 text-status-safe"
                        }`}
                      >
                        {results.model_2.is_phishing ? "Phishing" : "Safe"}
                      </span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          Confidence:
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatConfidence(results.model_2.confidence)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          Risk Level:
                        </span>
                        <span
                          className={`text-sm font-medium ${getRiskLevelColor(
                            results.model_2.risk_level,
                          )}`}
                        >
                          {results.model_2.risk_level.charAt(0).toUpperCase() +
                            results.model_2.risk_level.slice(1)}
                        </span>
                      </div>
                    </div>
                    {results.model_2.explanation && (
                      <div className="mt-2 rounded-lg bg-gray-50 p-2 dark:bg-gray-700">
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {results.model_2.explanation}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Fallback to legacy format if dual-model data not available */}
          {!results.combined_assessment &&
            !results.model_1 &&
            !results.model_2 && (
              <div className="mb-6">
                <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
                  Analysis Summary
                </h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                    <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                      Confidence
                    </p>
                    <p className="text-xl font-semibold text-gray-900 dark:text-white">
                      {results.confidence
                        ? formatConfidence(results.confidence)
                        : "N/A"}
                    </p>
                  </div>
                  <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                    <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                      Risk Level
                    </p>
                    <p
                      className={`text-xl font-semibold ${getRiskLevelColor(
                        results.risk_level || "unknown",
                      )}`}
                    >
                      {results.risk_level
                        ? results.risk_level.charAt(0).toUpperCase() +
                          results.risk_level.slice(1)
                        : "Unknown"}
                    </p>
                  </div>
                </div>
              </div>
            )}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="mb-6"
        >
          <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
            Detailed Analysis
          </h3>
          <p className="mb-6 text-gray-600 dark:text-gray-300">
            Comprehensive SMS analysis results
          </p>

          {/* Scan Engines Section */}
          {results.scanEngines && results.scanEngines.length > 0 && (
            <div className="mb-6">
              <h4 className="text-md mb-2 font-medium text-gray-900 dark:text-white">
                Scan Engines
              </h4>
              <div className="space-y-4">
                {results.scanEngines.map((engine, index) => (
                  <div
                    key={index}
                    className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <h5 className="font-medium text-gray-900 dark:text-white">
                        {engine.name}
                      </h5>
                      <span
                        className={`rounded-full px-2 py-1 text-xs font-medium ${
                          engine.result.toLowerCase() === "phishing"
                            ? "bg-status-danger/10 text-status-danger"
                            : "bg-status-safe/10 text-status-safe"
                        }`}
                      >
                        {engine.result}
                      </span>
                    </div>
                    <div className="grid grid-cols-1 gap-2 text-sm md:grid-cols-2">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Confidence:
                        </span>{" "}
                        <span className="font-medium text-gray-900 dark:text-white">
                          {typeof engine.confidence === "number"
                            ? engine.confidence > 1
                              ? engine.confidence.toFixed(2) + "%"
                              : (engine.confidence * 100).toFixed(2) + "%"
                            : engine.confidence}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Risk Level:
                        </span>{" "}
                        <span
                          className={`font-medium ${getRiskLevelColor(engine.risk_level)}`}
                        >
                          {engine.risk_level}
                        </span>
                      </div>
                      {engine.details && (
                        <div className="col-span-2">
                          <span className="text-gray-500 dark:text-gray-400">
                            Details:
                          </span>{" "}
                          <span className="text-gray-900 dark:text-white">
                            {engine.details}
                          </span>
                        </div>
                      )}
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Version:
                        </span>{" "}
                        <span className="text-gray-900 dark:text-white">
                          {engine.version}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Updated:
                        </span>{" "}
                        <span className="text-gray-900 dark:text-white">
                          {new Date(engine.updateDate).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mb-6">
            <div className="grid grid-cols-1 gap-4">
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  Message Type
                </p>
                <p className="text-base font-medium text-gray-900 dark:text-white">
                  SMS Text
                </p>
              </div>
              <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
                <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                  Phishing Risk
                </p>
                <p
                  className={`text-base font-medium ${getRiskLevelColor(
                    results.risk_level,
                  )}`}
                >
                  {results.risk_level.charAt(0).toUpperCase() +
                    results.risk_level.slice(1)}
                </p>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="text-md mb-2 font-medium text-gray-900 dark:text-white">
              Raw Analysis Data
            </h4>
            <div className="overflow-x-auto rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900">
              <pre className="whitespace-pre-wrap text-sm text-gray-800 dark:text-gray-300">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          </div>
        </motion.div>
      )}

      <div className="flex justify-end">
        <motion.button
          onClick={onAnalyzeAgain}
          className="rounded-lg bg-purple-600 px-6 py-3 text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:bg-purple-600 dark:hover:bg-purple-700"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          Analyze Again
        </motion.button>
      </div>
    </div>
  );
}
