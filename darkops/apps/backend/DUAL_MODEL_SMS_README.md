# Dual-Model SMS Phishing Detection System

This document describes the implementation of the dual-model SMS phishing detection system in DarkOps, which combines two AI models for enhanced accuracy in detecting SMS phishing attempts.

## Overview

The dual-model system uses:
- **Model 1**: BERT-Enhanced transformer model (existing system)
- **Model 2**: Google Gemini API (newly integrated)

Both models analyze the same SMS content in parallel and provide a combined assessment for improved accuracy.

## Architecture

```
SMS Message Input
       ↓
┌─────────────────┐
│ DualModelSMS    │
│ Service         │
└─────────────────┘
       ↓
┌─────────────────┐    ┌─────────────────┐
│ Model 1         │    │ Model 2         │
│ (BERT-Enhanced) │    │ (Gemini API)    │
│ via AI Service  │    │ Direct API      │
└─────────────────┘    └─────────────────┘
       ↓                       ↓
┌─────────────────────────────────────────┐
│ Combined Assessment Engine              │
│ - Consensus detection                   │
│ - Confidence weighting                  │
│ - Risk level determination              │
└─────────────────────────────────────────┘
       ↓
┌─────────────────┐
│ Unified Response│
│ Format          │
└─────────────────┘
```

## Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# Gemini API Configuration
GEMINI_API_KEY=AIzaSyDg6-0aqm-1ym5nRbPM-X1ph-jcJoVZASU
```

### API Key Setup

1. Get a Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add it to your environment variables
3. The system will automatically detect and initialize the Gemini service

## Response Format

The dual-model system returns a comprehensive response that includes:

### Legacy Fields (Backward Compatibility)
```json
{
  "prediction": "phishing" | "ham",
  "is_phishing": boolean,
  "confidence": number,
  "risk_level": "low" | "medium" | "high"
}
```

### Dual-Model Results
```json
{
  "model_1": {
    "is_phishing": boolean,
    "confidence": number,
    "risk_level": string,
    "model_name": "BERT-Enhanced",
    "explanation": string,
    "status": "success" | "error"
  },
  "model_2": {
    "is_phishing": boolean,
    "confidence": number,
    "risk_level": string,
    "model_name": "Gemini-1.5-Flash",
    "explanation": string,
    "status": "success" | "error"
  }
}
```

### Combined Assessment
```json
{
  "combined_assessment": {
    "is_phishing": boolean,
    "confidence": number,
    "risk_level": string,
    "consensus": boolean,
    "explanation": string
  }
}
```

### Metadata
```json
{
  "analysis_timestamp": "2024-01-01T00:00:00.000Z",
  "models_used": ["BERT-Enhanced", "Gemini-1.5-Flash"]
}
```

## Error Handling & Fallback

The system implements graceful fallback behavior:

1. **Both Models Available**: Uses both models and provides combined assessment
2. **Model 1 Only**: Falls back to BERT-Enhanced model if Gemini is unavailable
3. **Model 2 Only**: Uses Gemini if BERT service is down (rare case)
4. **No Models**: Returns safe defaults with error indication

## Consensus Logic

The combined assessment uses the following logic:

### When Models Agree
- Uses the agreed-upon classification
- Takes the higher risk level
- Averages confidence scores with Model 1 weighted at 60%, Model 2 at 40%

### When Models Disagree
- Uses the result from the model with higher confidence
- If confidence is equal, defaults to safer classification (not phishing)
- Provides detailed explanation of the disagreement

## Testing

Run the test script to verify the dual-model system:

```bash
cd darkops/apps/backend
node test-dual-model.js
```

## API Usage

### Endpoint
```
POST /ai/sms/detect-phishing
```

### Request
```json
{
  "message": "Your SMS message content here"
}
```

### Response
```json
{
  "prediction": "phishing",
  "is_phishing": true,
  "confidence": 0.85,
  "risk_level": "high",
  "model_1": {
    "is_phishing": true,
    "confidence": 0.82,
    "risk_level": "high",
    "model_name": "BERT-Enhanced",
    "status": "success"
  },
  "model_2": {
    "is_phishing": true,
    "confidence": 0.88,
    "risk_level": "high",
    "model_name": "Gemini-1.5-Flash",
    "explanation": "Message contains urgent language and suspicious link",
    "status": "success"
  },
  "combined_assessment": {
    "is_phishing": true,
    "confidence": 0.85,
    "risk_level": "high",
    "consensus": true,
    "explanation": "Both models agree: PHISHING. Model 1 confidence: 82.0%, Model 2 confidence: 88.0%"
  },
  "analysis_timestamp": "2024-01-01T00:00:00.000Z",
  "models_used": ["BERT-Enhanced", "Gemini-1.5-Flash"]
}
```

## Monitoring & Logging

The system provides detailed logging with consistent emoji formatting:

- ✅ Success operations
- ❌ Error conditions  
- ⚠️ Warning states
- 🔍 Analysis start
- 📊 Results summary

## Performance Considerations

- Models run in parallel for optimal performance
- Gemini API calls are cached when possible
- Fallback ensures service availability even if one model fails
- Response times typically under 2-3 seconds for both models

## Security

- Gemini API key is stored securely in environment variables
- No SMS content is logged in production
- API calls use proper authentication headers
- Rate limiting applies to Gemini API calls
