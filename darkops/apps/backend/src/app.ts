import createApp from "@/lib/create-app.js";
import configureOpenAPI from "@/lib/configure-openAPI.js";
import index from "@/routes/index.route.js";
import auth from "@/routes/auth/auth.index.ts";
import ai from "@/routes/ai/ai.index.ts";
import stats from "@/routes/stats/stats.index.ts";
import email from "@/routes/email/email.index.ts";
import { connectMongoDB } from "./db/mongodb.ts";
import { checkAIServiceConnection } from "./utils/aiServiceChecker.ts";
import yoga from "./graphql/index.ts"; // Import the yoga instance

// Connect to MongoDB first
await connectMongoDB();

// Check AI service connection
await checkAIServiceConnection();

const app = createApp();

// Configure OpenAPI
configureOpenAPI(app);

// Mount GraphQL app - use the fetch handler from yoga
app.use("/graphql", async (c) => {
  // GraphQL Yoga expects a standard Request object
  const response = await yoga.fetch(c.req.raw, {
    // Pass any additional context you need
    context: { c },
  });

  // Convert the Response to Hono's expected format
  return new Response(response.body, {
    status: response.status,
    headers: response.headers,
  });
});

// Mount All Route Modules
const routes = [index, auth, ai, stats, email];
routes.forEach((route) => {
  app.route("/", route);
});

export default app;
