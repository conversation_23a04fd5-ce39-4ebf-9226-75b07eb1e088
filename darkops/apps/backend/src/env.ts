import { z } from "zod";
import { config } from "dotenv";

config();
const EnvSchema = z.object({
  NODE_ENV: z.string().default("development"),
  PORT: z.coerce.number().default(9999),
  DATABASE_URL: z.string().url(),
  EMAIL_USER: z.string().email(),
  EMAIL_PASS: z.string(),
  EMAIL_FROM: z.string().default("DarkOps Security"),
  JWT_SECRET: z.string(),
  GOOGLE_CLIENT_ID: z.string(),
  GOOGLE_CLIENT_SECRET: z.string(),
  GOOGLE_CALLBACK_URL: z.string().url(),
  MONGO_ATLAS_URI: z.string().url(),
  AI_SERVICE_URL: z.string().url().default("http://0.0.0.0:8000"),
  GEMINI_API_KEY: z.string().optional(),
});

export type Env = z.infer<typeof EnvSchema>;

let env: Env;
env = EnvSchema.parse(process.env);

export default env;
