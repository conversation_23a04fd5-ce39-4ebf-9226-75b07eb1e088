import axios from "axios";
import env from "@/env.js";

const AI_SERVICE_URL = env.AI_SERVICE_URL;

/**
 * Check AI service connection status and log the result
 * This function verifies connectivity to the FastAPI AI service used for phishing detection
 */
export async function checkAIServiceConnection(): Promise<void> {
  try {
    // Make a simple health check request to the AI service root endpoint
    const response = await axios.get(`${AI_SERVICE_URL}/`, {
      timeout: 10000, // 10 second timeout
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Check if the response indicates the service is operational
    if (response.status === 200 && response.data?.status === 'operational') {
      console.log('✅ AI service is ready for phishing detection');
    } else {
      console.log('⚠️  AI service responded but may not be fully operational');
      console.log(`   Status: ${response.status}, Data:`, response.data);
    }
  } catch (error: any) {
    console.error('❌ AI service connection error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   Please ensure the AI service is running on', AI_SERVICE_URL);
    } else if (error.code === 'ETIMEDOUT') {
      console.log('   Connection timeout - AI service may be slow to respond');
    } else if (error.response) {
      console.log(`   HTTP ${error.response.status}: ${error.response.statusText}`);
    }
    
    console.log('   Some AI-powered features may not be available until the service is restored');
  }
}
