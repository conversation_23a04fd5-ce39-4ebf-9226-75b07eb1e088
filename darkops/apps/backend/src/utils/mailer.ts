import nodemailer from "nodemailer";
import mjml2html from "mjml";
import env from "@/env.js";

// Create Nodemailer transporter with Gmail SMTP
const createTransporter = () => {
  return nodemailer.createTransport({
    host: "smtp.gmail.com",
    port: 465,
    secure: true, // Use SSL
    auth: {
      user: env.EMAIL_USER,
      pass: env.EMAIL_PASS, // This should be an App Password from Google
    },
    // Only enable these for debugging, comment out in production
    // debug: true,
    // logger: true
  });
};

// Initialize transporter
let transporter = createTransporter();

// Verify the connection configuration
transporter.verify(function (error, success) {
  if (error) {
    console.error("❌ SMTP connection error:", error);
    console.log(
      "   Please make sure you're using an App Password from Google, not your regular password."
    );
    console.log(
      "   Instructions: https://support.google.com/accounts/answer/185833"
    );
  } else {
    console.log("✅ SMTP server is ready for email delivery");
  }
});

const getEmailContent = (
  otp: string,
  type: "verification" | "password_reset"
) => {
  const title =
    type === "verification"
      ? "Verify Your Email Address"
      : "Reset Your Password";
  const greeting = `Hello,`;
  const message =
    type === "verification"
      ? "Please use the following One-Time Password (OTP) to verify your email address for your DarkOps account. This code is valid for 10 minutes."
      : "We received a request to reset the password for your DarkOps account. Use the One-Time Password (OTP) below to proceed. This code is valid for 10 minutes.";
  const subject =
    type === "verification"
      ? "Verify Your DarkOps Account"
      : "DarkOps Password Reset Request";
  const companyName = "DarkOps";
  const logoUrl =
    "https://res.cloudinary.com/dst8arhox/image/upload/v1746102596/DarkOps_amkm7z.svg";

  // Define Color Palette (Dark Theme)
  const colors = {
    background: "#121212", // Very dark background
    cardBackground: "#1E1E1E", // Dark card background
    cardBorder: "#333333", // Subtle border for the card
    textPrimary: "#FFFFFF", // White for main text
    textSecondary: "#B0B0B0", // Light grey for secondary text
    accent: "#6200EA", // Purple accent color
    accentGradient: "linear-gradient(135deg, #6200EA, #9D50BB)", // Gradient accent
    accentText: "#FFFFFF", // White text on accent background
    footerText: "#707070", // Medium grey for footer
    divider: "#333333", // Dark divider
  };

  const template = `
    <mjml>
      <mj-head>
        <mj-preview>${subject}</mj-preview>
        <mj-attributes>
          <mj-all font-family="'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" />
          <mj-text font-size="16px" color="${colors.textPrimary}" line-height="1.6" />
          <mj-divider border-color="${colors.divider}" />
        </mj-attributes>
        <mj-style>
          .otp-code {
            display: inline-block;
            font-size: 32px;
            font-weight: 600;
            color: ${colors.accentText};
            background: ${colors.accentGradient};
            padding: 16px 32px;
            border-radius: 10px;
            margin: 24px 0;
            text-align: center;
            letter-spacing: 6px;
            box-shadow: 0 4px 10px rgba(98, 0, 234, 0.3);
          }
          .card {
            border-radius: 16px;
            border: 1px solid ${colors.cardBorder};
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            overflow: hidden;
          }
          .footer-link {
            color: ${colors.footerText};
            text-decoration: none;
          }
          .accent-text {
            color: #9D50BB;
            font-weight: 500;
          }
          .otp-container mj-text {
            text-align: center;
          }
          .header-glow {
            box-shadow: 0 0 20px rgba(98, 0, 234, 0.1);
          }
        </mj-style>
      </mj-head>
      <mj-body background-color="${colors.background}">

        <!-- Header with subtle glow effect -->
        <mj-section padding="40px 0 30px 0" css-class="header-glow">
          <mj-column>
            <mj-image width="180px" src="${logoUrl}" alt="${companyName} Logo" padding="0" />
          </mj-column>
        </mj-section>

        <!-- Main Content Card -->
        <mj-section padding="0 20px 30px 20px">
          <mj-column background-color="${colors.cardBackground}" css-class="card" padding="35px 25px">

            <!-- Title with accent -->
            <mj-text align="center" font-size="28px" font-weight="700" color="${colors.textPrimary}" padding-bottom="15px">
              ${title}
            </mj-text>

            <!-- Greeting & Message -->
            <mj-text align="left" color="${colors.textPrimary}" padding-top="10px" padding-bottom="5px" font-weight="500">
              ${greeting}
            </mj-text>
            <mj-text align="left" color="${colors.textSecondary}" padding-bottom="15px">
              ${message}
            </mj-text>

            <!-- OTP Code Section -->
            <mj-wrapper css-class="otp-container" padding="15px 0 25px 0">
              <mj-text align="center" padding="0">
                <span class="otp-code">${otp}</span>
              </mj-text>
            </mj-wrapper>

            <!-- Security Note -->
            <mj-text align="center" color="${colors.textSecondary}" font-size="14px" padding-top="10px">
              If you didn't request this, you can safely ignore this email.
            </mj-text>

          </mj-column>
        </mj-section>

        <!-- Footer Section -->
        <mj-section padding="10px 20px 40px 20px">
          <mj-column>
            <mj-divider border-width="1px" padding="0 0 20px 0" />
            <mj-text align="center" color="${colors.footerText}" font-size="13px" line-height="1.5">
              © ${new Date().getFullYear()} <span class="accent-text">${companyName}</span>. All rights reserved.
            </mj-text>
          </mj-column>
        </mj-section>

      </mj-body>
    </mjml>
  `;

  return {
    subject,
    text: `${greeting}\n\n${message}\n\nYour One-Time Password is: ${otp}\n\nThis code expires in 10 minutes.\n\nIf you didn't request this, please ignore this email.\n\n© ${new Date().getFullYear()} ${companyName}`,
    mjmlTemplate: template,
  };
};

export const sendOTPEmail = async (
  email: string,
  otp: string,
  type: "verification" | "password_reset"
) => {
  const { subject, text, mjmlTemplate } = getEmailContent(otp, type);
  const { html } = mjml2html(mjmlTemplate);

  // Maximum number of retry attempts
  const maxRetries = 2;
  let retries = 0;
  let lastError;

  while (retries <= maxRetries) {
    try {
      // Send email using Nodemailer with Gmail SMTP
      const info = await transporter.sendMail({
        from: `"${env.EMAIL_FROM}" <${env.EMAIL_USER}>`,
        to: email,
        subject,
        text,
        html,
      });

      console.log(`✅ Email sent to ${email}: ${info.messageId}`);
      return { id: info.messageId };
    } catch (e: any) {
      lastError = e;
      console.error(
        `❌ Error sending email (attempt ${retries + 1}/${maxRetries + 1}):`,
        e.message
      );

      // If it's an authentication error, no point in retrying with the same credentials
      if (e.code === "EAUTH") {
        console.error(
          "   Authentication failed. Please check your Gmail credentials and make sure you're using an App Password."
        );
        break;
      }

      // If it's a connection error, try to recreate the transporter
      if (e.code === "ECONNECTION" || e.code === "ETIMEDOUT") {
        console.log("   Connection issue detected, recreating transporter...");
        transporter = createTransporter();
      }

      retries++;
      if (retries <= maxRetries) {
        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, retries) * 1000;
        console.log(`Retrying in ${delay / 1000} seconds...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  // If we've exhausted all retries, throw the last error
  console.error("❌ Failed to send email after multiple attempts");
  throw lastError;
};
