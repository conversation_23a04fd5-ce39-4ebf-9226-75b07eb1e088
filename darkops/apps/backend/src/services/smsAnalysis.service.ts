import { ScanHistory } from "@/db/ScanHistory.ts";
import { DashboardStats } from "@/db/DashboardStats.ts";

/**
 * Service for handling SMS analysis data extraction and processing
 */
export class SmsAnalysisService {
  /**
   * Extract basic SMS information from analysis result
   * @param result The SMS analysis result
   * @returns SMS information object
   */
  static extractSmsInfo(result: any): any {
    const smsInfo: any = {};

    // Extract message content if available
    if (result?.message) {
      smsInfo.message = result.message;
    }

    // Extract sender information if available
    if (result?.sender) {
      smsInfo.sender = result.sender;
    }

    // Extract timestamp if available
    if (result?.timestamp) {
      smsInfo.timestamp = result.timestamp;
    }

    // Extract analysis timestamp if available
    if (result?.analysis_timestamp) {
      smsInfo.analysisTimestamp = result.analysis_timestamp;
    } else {
      smsInfo.analysisTimestamp = new Date().toISOString();
    }

    // Extract message length
    if (result?.message) {
      smsInfo.messageLength = result.message.length;
    }

    // Extract phishing detection result
    if (result?.is_phishing !== undefined) {
      smsInfo.isPhishing = result.is_phishing;
    }

    // Extract confidence
    if (result?.confidence !== undefined) {
      smsInfo.confidence = result.confidence;
    }

    // Extract risk level
    if (result?.risk_level) {
      smsInfo.riskLevel = result.risk_level;
    }

    // Extract message type (if available)
    if (result?.message_type) {
      smsInfo.messageType = result.message_type;
    }

    // Extract recipient (if available)
    if (result?.recipient) {
      smsInfo.recipient = result.recipient;
    }

    return smsInfo;
  }

  /**
   * Extract content analysis information from SMS analysis result
   * @param result The SMS analysis result
   * @returns Content analysis information object
   */
  static extractContentAnalysis(result: any): any {
    const contentAnalysis: any = {};

    // Extract risk level if available
    if (result?.risk_level) {
      contentAnalysis.riskLevel = result.risk_level;
    }

    // Extract suspicious keywords if available
    if (result?.suspicious_keywords) {
      contentAnalysis.suspiciousKeywords = result.suspicious_keywords;
    }

    // Extract risk indicators if available
    if (result?.risk_indicators) {
      contentAnalysis.riskIndicators = result.risk_indicators;
    }

    // Extract content categories if available
    if (result?.content_categories) {
      contentAnalysis.contentCategories = result.content_categories;
    }

    // Extract language information if available
    if (result?.language) {
      contentAnalysis.language = result.language;
    }

    // Extract sentiment analysis if available
    if (result?.sentiment) {
      contentAnalysis.sentiment = result.sentiment;
    }

    // Extract phishing detection information
    if (result?.is_phishing !== undefined) {
      contentAnalysis.isPhishing = result.is_phishing;

      // Add confidence if available
      if (result?.confidence !== undefined) {
        contentAnalysis.confidence = result.confidence;
      }
    }

    // Extract scan engines if available
    if (result?.scanEngines && Array.isArray(result.scanEngines)) {
      contentAnalysis.scanEnginesCount = result.scanEngines.length;

      // Extract detection results
      const detectionResults = result.scanEngines.map((engine: any) => ({
        name: engine.name,
        result: engine.result,
        confidence: engine.confidence,
      }));

      contentAnalysis.detectionResults = detectionResults;
    }

    // Extract threat types if available
    if (result?.threat_types && Array.isArray(result.threat_types)) {
      contentAnalysis.threatTypes = result.threat_types;
    }

    // Extract spam score if available
    if (result?.spam_score !== undefined) {
      contentAnalysis.spamScore = result.spam_score;
    }

    // Extract message intent if available
    if (result?.message_intent) {
      contentAnalysis.messageIntent = result.message_intent;
    }

    return contentAnalysis;
  }

  /**
   * Extract URL information from SMS analysis result
   * @param result The SMS analysis result
   * @returns URL information object
   */
  static extractUrlInfo(result: any): any {
    const urlInfo: any = {};

    // Extract URLs if available
    if (result?.urls) {
      urlInfo.urls = result.urls;
    }

    // Extract shortened URLs if available
    if (result?.shortened_urls) {
      urlInfo.shortenedUrls = result.shortened_urls;
    }

    // Extract URL analysis if available
    if (result?.url_analysis) {
      urlInfo.urlAnalysis = result.url_analysis;

      // Count malicious, suspicious, and clean URLs
      let maliciousCount = 0;
      let suspiciousCount = 0;
      let cleanCount = 0;

      result.url_analysis.forEach((url: any) => {
        if (url.malicious) {
          maliciousCount++;
        } else if (url.suspicious) {
          suspiciousCount++;
        } else {
          cleanCount++;
        }
      });

      urlInfo.maliciousUrlsCount = maliciousCount;
      urlInfo.suspiciousUrlsCount = suspiciousCount;
      urlInfo.cleanUrlsCount = cleanCount;
    }

    return urlInfo;
  }

  /**
   * Extract scan engines from SMS analysis result
   * @param result The SMS analysis result
   * @returns Array of scan engines with their results
   */
  static extractScanEngines(result: any): Array<{
    name: string;
    result: string;
    confidence: number;
    risk_level?: string;
    details?: string;
    updateDate?: string;
    version?: string;
  }> {
    // Handle dual-model response format first
    if (result?.model_1 || result?.model_2) {
      const scanEngines = [];

      // Add Model 1 (BERT-Enhanced) scan engine
      if (result.model_1 && result.model_1.status === "success") {
        const model1Engine = this.createScanEngine(
          result.model_1,
          "Model 1 (BERT-Enhanced)",
          "1.0"
        );
        if (model1Engine) scanEngines.push(model1Engine);
      }

      // Add Model 2 (Gemini) scan engine
      if (result.model_2 && result.model_2.status === "success") {
        const model2Engine = this.createScanEngine(
          result.model_2,
          "Model 2 (Gemini)",
          "1.5"
        );
        if (model2Engine) scanEngines.push(model2Engine);
      }

      // Add combined assessment as a scan engine
      if (result.combined_assessment) {
        const combinedEngine = this.createScanEngine(
          result.combined_assessment,
          "Combined Assessment",
          "2.0",
          result.combined_assessment.explanation
        );
        if (combinedEngine) scanEngines.push(combinedEngine);
      }

      return scanEngines;
    }

    // If scanEngines are provided and not empty, return them directly
    if (
      result?.scanEngines &&
      Array.isArray(result.scanEngines) &&
      result.scanEngines.length > 0
    ) {
      // Ensure all scan engines have the required fields
      return result.scanEngines.map((engine: any) => {
        // Convert risk_level to uppercase if it exists
        let riskLevel = null;
        if (engine.risk_level) {
          // Map lowercase risk levels to uppercase enum values
          const riskLevelMap: Record<string, string> = {
            low: "LOW",
            medium: "MEDIUM",
            high: "HIGH",
            critical: "CRITICAL",
          };

          // Ensure engine.risk_level is a string and convert to lowercase
          const riskLevelKey =
            typeof engine.risk_level === "string"
              ? engine.risk_level.toLowerCase()
              : String(engine.risk_level).toLowerCase();

          riskLevel = riskLevelMap[riskLevelKey] || null;

          // Log the risk level conversion for debugging
          console.log(
            `SMS Analysis: Converting scan engine risk level from "${engine.risk_level}" to "${riskLevel}"`
          );
        }

        return {
          name: engine.name || "Unknown Engine",
          result: engine.result || "Unknown",
          confidence:
            typeof engine.confidence === "number" ? engine.confidence : 0,
          risk_level: riskLevel,
          details: engine.details || `Confidence: ${engine.confidence}%`,
          updateDate: result.analysis_timestamp || new Date().toISOString(),
          version: engine.version || "1.0",
        };
      });
    }

    // If scanEngines are empty or not provided, but we have is_phishing and risk_level,
    // create a default scan engine based on those values
    if (result?.is_phishing !== undefined) {
      // Convert confidence to a percentage value if it's between 0 and 1
      let confidenceValue =
        typeof result.confidence === "number" ? result.confidence : 0;
      if (confidenceValue > 0 && confidenceValue < 1) {
        confidenceValue = confidenceValue * 100;
      }

      // Determine risk level if not provided
      let riskLevel = result.risk_level || "unknown";
      if (!result.risk_level) {
        if (result.is_phishing) {
          if (confidenceValue > 90) {
            riskLevel = "high";
          } else if (confidenceValue > 70) {
            riskLevel = "medium";
          } else {
            riskLevel = "low";
          }
        } else {
          riskLevel = "low";
        }
      }

      // Convert risk level to uppercase for MongoDB schema validation
      const riskLevelMap: Record<string, string> = {
        low: "LOW",
        medium: "MEDIUM",
        high: "HIGH",
        critical: "CRITICAL",
        unknown: null,
      };

      // Map the lowercase risk level to uppercase or null if not found
      const normalizedRiskLevel = riskLevelMap[riskLevel.toLowerCase()] || null;

      // Log the risk level conversion for debugging
      console.log(
        `SMS Analysis: Converting risk level from "${riskLevel}" to "${normalizedRiskLevel}"`
      );

      // Ensure the risk level is valid for MongoDB schema
      if (
        normalizedRiskLevel !== null &&
        !["LOW", "MEDIUM", "HIGH", "CRITICAL"].includes(normalizedRiskLevel)
      ) {
        console.warn(
          `SMS Analysis: Invalid risk level "${normalizedRiskLevel}" defaulting to null`
        );
      }

      const defaultEngine = {
        name: "SMS Phishing Detection",
        result: result.is_phishing ? "Phishing" : "Clean",
        confidence: confidenceValue,
        risk_level: normalizedRiskLevel,
        details: `Confidence: ${confidenceValue.toFixed(2)}%`,
        updateDate: result.analysis_timestamp || new Date().toISOString(),
        version: "1.0",
      };

      return [defaultEngine];
    }

    // If no scan engines can be created, return an empty array
    return [];
  }

  /**
   * Create a scan engine object from model result
   * @param modelResult The model analysis result
   * @param engineName Name of the scan engine
   * @param version Version of the engine
   * @param customDetails Custom details string
   * @returns Scan engine object or null if invalid
   */
  private static createScanEngine(
    modelResult: any,
    engineName: string,
    version: string,
    customDetails?: string
  ): {
    name: string;
    result: string;
    confidence: number;
    risk_level?: string;
    details?: string;
    updateDate?: string;
    version?: string;
  } | null {
    if (!modelResult) return null;

    // Get confidence value
    let confidenceValue =
      typeof modelResult.confidence === "number" ? modelResult.confidence : 0;

    // Ensure confidence is in percentage format (0-100)
    if (confidenceValue > 0 && confidenceValue <= 1) {
      confidenceValue = confidenceValue * 100;
    }

    // Get risk level and normalize it
    const riskLevel = modelResult.risk_level || "unknown";
    const normalizedRiskLevel = this.normalizeRiskLevel(riskLevel);

    // Create details string
    const details =
      customDetails ||
      `${engineName} - Confidence: ${confidenceValue.toFixed(2)}%${modelResult.explanation ? ` | ${modelResult.explanation}` : ""}`;

    return {
      name: engineName,
      result: modelResult.is_phishing ? "Phishing" : "Clean",
      confidence: confidenceValue,
      risk_level: normalizedRiskLevel,
      details: details,
      updateDate: modelResult.analysis_timestamp || new Date().toISOString(),
      version: version,
    };
  }

  /**
   * Normalize risk level for MongoDB schema validation
   * @param riskLevel Raw risk level string
   * @returns Normalized risk level or null
   */
  private static normalizeRiskLevel(riskLevel: string): string | null {
    if (typeof riskLevel !== "string") {
      console.warn(
        `SMS Analysis: Invalid risk level type "${typeof riskLevel}", expected string`
      );
      return null;
    }

    // Convert risk level to uppercase for MongoDB schema validation
    const riskLevelMap: Record<string, string> = {
      low: "LOW",
      medium: "MEDIUM",
      high: "HIGH",
      critical: "CRITICAL",
      unknown: null,
    };

    // Map the lowercase risk level to uppercase or null if not found
    const normalizedRiskLevel = riskLevelMap[riskLevel.toLowerCase()] || null;

    // Log the risk level conversion for debugging
    console.log(
      `SMS Analysis: Converting risk level from "${riskLevel}" to "${normalizedRiskLevel}"`
    );

    // Ensure the risk level is valid for MongoDB schema
    if (
      normalizedRiskLevel !== null &&
      !["LOW", "MEDIUM", "HIGH", "CRITICAL"].includes(normalizedRiskLevel)
    ) {
      console.warn(
        `SMS Analysis: Invalid risk level "${normalizedRiskLevel}" defaulting to null`
      );
      return null;
    }

    return normalizedRiskLevel;
  }

  /**
   * Extract findings from SMS analysis result
   * @param result The SMS analysis result
   * @returns Array of findings
   */
  static extractFindings(result: any): Array<{
    type: string;
    description: string;
    severity: string;
    details?: any;
  }> {
    const findings = [];

    // Check for phishing detection (handle multiple possible formats)
    if (
      result?.prediction?.toLowerCase() === "phishing" ||
      result?.is_phishing === true ||
      (result?.scanEngines &&
        Array.isArray(result.scanEngines) &&
        result.scanEngines.some(
          (engine: any) => engine.result?.toLowerCase() === "phishing"
        ))
    ) {
      // Get confidence value for details
      let confidenceValue =
        typeof result.confidence === "number"
          ? result.confidence
          : (result.scanEngines &&
              Array.isArray(result.scanEngines) &&
              result.scanEngines[0]?.confidence) ||
            0;

      // Ensure confidence is in percentage format
      if (confidenceValue > 0 && confidenceValue < 1) {
        confidenceValue = confidenceValue * 100;
      }

      findings.push({
        type: "PHISHING",
        description: "This SMS was detected as a phishing attempt",
        severity: "HIGH",
        details: {
          confidence: confidenceValue,
          is_phishing: true,
          risk_level: result.risk_level || "high",
        },
      });
    }

    // Check for risk level
    if (result?.risk_level) {
      const riskLevel = result.risk_level.toLowerCase();
      let severity = "MEDIUM";

      if (riskLevel === "high" || riskLevel === "critical") {
        severity = "HIGH";
      } else if (riskLevel === "low") {
        severity = "LOW";
      }

      findings.push({
        type: "RISK_ASSESSMENT",
        description: `SMS has been assessed with ${riskLevel} risk level`,
        severity: severity,
        details: {
          risk_level: riskLevel,
          confidence:
            typeof result.confidence === "number" ? result.confidence : 0,
        },
      });
    }

    // Check for suspicious keywords
    if (result?.suspicious_keywords && result.suspicious_keywords.length > 0) {
      findings.push({
        type: "SUSPICIOUS_KEYWORDS",
        description: `SMS contains ${result.suspicious_keywords.length} suspicious keywords`,
        severity: "MEDIUM",
        details: {
          keywords: result.suspicious_keywords,
          count: result.suspicious_keywords.length,
        },
      });
    }

    // Check for malicious URLs
    if (result?.url_analysis && result.url_analysis.length > 0) {
      const maliciousUrls = result.url_analysis.filter(
        (url: any) => url.malicious
      );

      if (maliciousUrls.length > 0) {
        findings.push({
          type: "MALICIOUS_URLS",
          description: `SMS contains ${maliciousUrls.length} malicious URLs`,
          severity: "HIGH",
          details: {
            urls: maliciousUrls.map((url: any) => url.url),
            count: maliciousUrls.length,
            analysis: maliciousUrls,
          },
        });
      }
    }

    // Check for shortened URLs (potential risk)
    if (result?.shortened_urls && result.shortened_urls.length > 0) {
      findings.push({
        type: "SHORTENED_URLS",
        description: `SMS contains ${result.shortened_urls.length} shortened URLs`,
        severity: "MEDIUM",
        details: {
          urls: result.shortened_urls,
          count: result.shortened_urls.length,
        },
      });
    }

    // Check for spam
    if (result?.spam_score !== undefined && result.spam_score > 0.7) {
      findings.push({
        type: "SPAM",
        description: "This SMS was detected as spam",
        severity: "MEDIUM",
        details: {
          spamScore: result.spam_score,
          threshold: 0.7,
        },
      });
    }

    // Check for risk indicators
    if (
      result?.risk_indicators &&
      Object.keys(result.risk_indicators).length > 0
    ) {
      findings.push({
        type: "RISK_INDICATORS",
        description: "SMS contains specific risk indicators",
        severity: "MEDIUM",
        details: result.risk_indicators,
      });
    }

    return findings;
  }

  /**
   * Calculate overall confidence score from scan engines
   * @param scanEngines Array of scan engines
   * @returns Confidence score (0-100)
   */
  static calculateConfidence(
    scanEngines: Array<{
      name: string;
      result: string;
      confidence: number;
      risk_level?: string;
    }>
  ): number {
    if (!scanEngines || scanEngines.length === 0) {
      return 0;
    }

    // Define weights for different scan engines
    const engineWeights: Record<string, number> = {
      "SMS Phishing Detection": 0.8,
      "Content Analysis": 0.6,
      "URL Analysis": 0.7,
      "Spam Detection": 0.5,
    };

    // Calculate weighted average of confidence scores
    let totalWeight = 0;
    let weightedConfidence = 0;

    scanEngines.forEach((engine) => {
      const weight = engineWeights[engine.name] || 0.5; // Default weight for unknown engines
      weightedConfidence += engine.confidence * weight;
      totalWeight += weight;
    });

    // If we have weights, calculate weighted average, otherwise use simple average
    if (totalWeight > 0) {
      return Math.round(weightedConfidence / totalWeight);
    } else {
      const totalConfidence = scanEngines.reduce(
        (sum, engine) => sum + engine.confidence,
        0
      );
      return Math.round(totalConfidence / scanEngines.length);
    }
  }

  /**
   * Get the most recent SMS scan for a user
   * @param userId User ID
   * @returns The most recent SMS scan or null if none exists
   */
  static async getLastSmsScan(userId: string): Promise<any> {
    return ScanHistory.findOne({
      userId,
      scanType: "SMS",
    }).sort({ createdAt: -1 });
  }
}
