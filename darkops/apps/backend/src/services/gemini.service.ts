import { GoogleGenerativeAI } from "@google/generative-ai";
import env from "@/env.js";

/**
 * Gemini API Service for SMS phishing detection
 * Provides Model 2 analysis alongside the existing BERT model (Model 1)
 */
export class GeminiService {
  private genAI: GoogleGenerativeAI | null = null;
  private model: any = null;
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  /**
   * Initialize the Gemini API client
   */
  private initialize(): void {
    try {
      console.log("🔧 Initializing Gemini API service...");
      console.log("🔑 Gemini API key present:", !!env.GEMINI_API_KEY);
      console.log("🔑 Gemini API key length:", env.GEMINI_API_KEY?.length || 0);

      if (!env.GEMINI_API_KEY) {
        console.log(
          "❌ Gemini API key not configured - Model 2 will be unavailable"
        );
        return;
      }

      if (env.GEMINI_API_KEY.length < 10) {
        console.log(
          "❌ Gemini API key appears to be invalid (too short) - Model 2 will be unavailable"
        );
        return;
      }

      this.genAI = new GoogleGenerativeAI(env.GEMINI_API_KEY);
      this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
      this.isInitialized = true;
      console.log("✅ Gemini API service initialized successfully");
      console.log("🎯 Model name: gemini-1.5-flash");
    } catch (error) {
      console.error("❌ Failed to initialize Gemini API service:", error);
      this.isInitialized = false;
    }
  }

  /**
   * Check if Gemini service is available
   */
  public isAvailable(): boolean {
    return this.isInitialized && this.model !== null;
  }

  /**
   * Analyze SMS message for phishing using Gemini API
   * @param message SMS message content to analyze
   * @returns Analysis result with phishing assessment
   */
  public async analyzeSMS(message: string): Promise<{
    is_phishing: boolean;
    confidence: number;
    risk_level: string;
    explanation: string;
    model_name: string;
    status: string;
  }> {
    console.log("🔍 Starting Gemini SMS analysis...");
    console.log("📱 Message length:", message.length);

    if (!this.isAvailable()) {
      console.log("❌ Gemini service not available for SMS analysis");
      throw new Error("Gemini API service is not available");
    }

    try {
      console.log("🚀 Calling Gemini API...");
      const prompt = this.buildPhishingAnalysisPrompt(message);
      console.log("📝 Prompt length:", prompt.length);

      const result = await this.model.generateContent(prompt);
      console.log("📡 Received response from Gemini API");

      const response = await result.response;
      const text = response.text();
      console.log("📄 Response text length:", text.length);
      console.log("📄 Raw response:", text.substring(0, 200) + "...");

      const parsedResult = this.parseGeminiResponse(text);
      console.log("✅ Gemini analysis completed successfully");
      console.log("📊 Result:", {
        is_phishing: parsedResult.is_phishing,
        confidence: parsedResult.confidence,
        risk_level: parsedResult.risk_level,
      });

      return parsedResult;
    } catch (error: any) {
      console.error("❌ Gemini API analysis failed:", error);
      console.error("❌ Error type:", error.constructor.name);
      console.error("❌ Error message:", error.message);
      console.error("❌ Error stack:", error.stack);
      throw new Error(`Gemini analysis failed: ${error.message}`);
    }
  }

  /**
   * Build the prompt for phishing analysis
   */
  private buildPhishingAnalysisPrompt(message: string): string {
    return `
You are an expert cybersecurity analyst specializing in SMS phishing detection. Analyze the following SMS message and determine if it's a phishing attempt.

SMS Message: "${message}"

Please analyze this message for phishing indicators such as:
- Urgent language or threats
- Requests for personal information
- Suspicious links or URLs
- Impersonation of legitimate organizations
- Grammar and spelling errors
- Unusual sender behavior
- Financial requests or offers

Respond with a JSON object in this exact format:
{
  "is_phishing": boolean,
  "confidence": number (0.0 to 1.0),
  "risk_level": "low" | "medium" | "high",
  "explanation": "Brief explanation of your analysis and key indicators found"
}

Be precise and only respond with the JSON object, no additional text.
`;
  }

  /**
   * Parse Gemini API response and extract analysis results
   */
  private parseGeminiResponse(responseText: string): {
    is_phishing: boolean;
    confidence: number;
    risk_level: string;
    explanation: string;
    model_name: string;
    status: string;
  } {
    try {
      // Clean the response text to extract JSON
      const cleanedText = responseText.trim();
      let jsonText = cleanedText;

      // Remove markdown code blocks if present
      if (cleanedText.includes("```json")) {
        const jsonMatch = cleanedText.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonText = jsonMatch[1];
        }
      } else if (cleanedText.includes("```")) {
        const jsonMatch = cleanedText.match(/```\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonText = jsonMatch[1];
        }
      }

      const parsed = JSON.parse(jsonText);

      // Validate required fields
      if (typeof parsed.is_phishing !== "boolean") {
        throw new Error("Invalid is_phishing field");
      }
      if (
        typeof parsed.confidence !== "number" ||
        parsed.confidence < 0 ||
        parsed.confidence > 1
      ) {
        throw new Error("Invalid confidence field");
      }
      if (!["low", "medium", "high"].includes(parsed.risk_level)) {
        throw new Error("Invalid risk_level field");
      }

      return {
        is_phishing: parsed.is_phishing,
        confidence: parsed.confidence,
        risk_level: parsed.risk_level,
        explanation: parsed.explanation || "No explanation provided",
        model_name: "Gemini-1.5-Flash",
        status: "success",
      };
    } catch (error: any) {
      console.error("❌ Failed to parse Gemini response:", error);
      console.error("Raw response:", responseText);

      // Return a fallback response
      return {
        is_phishing: false,
        confidence: 0.0,
        risk_level: "low",
        explanation: `Failed to parse Gemini response: ${error.message}`,
        model_name: "Gemini-1.5-Flash",
        status: "error",
      };
    }
  }

  /**
   * Test the Gemini API connection
   */
  public async testConnection(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const testMessage = "Hello, this is a test message.";
      await this.analyzeSMS(testMessage);
      console.log("✅ Gemini API connection test successful");
      return true;
    } catch (error) {
      console.error("❌ Gemini API connection test failed:", error);
      return false;
    }
  }
}

// Export singleton instance
export const geminiService = new GeminiService();
