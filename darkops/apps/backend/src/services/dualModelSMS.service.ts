import axios from "axios";
import env from "@/env.js";
import { geminiService } from "./gemini.service.js";
import type { ModelResult } from "@/routes/ai/ai.schemas.js";

const AI_SERVICE_URL = env.AI_SERVICE_URL;

/**
 * Dual Model SMS Analysis Service
 * Coordinates analysis between Model 1 (BERT/Enhanced) and Model 2 (Gemini API)
 */
export class DualModelSMSService {
  /**
   * Analyze SMS message using both Model 1 and Model 2
   * @param message SMS message content to analyze
   * @param authHeaders Authentication headers for AI service
   * @returns Combined analysis results from both models
   */
  public static async analyzeSMS(
    message: string,
    authHeaders: Record<string, string>
  ): Promise<{
    // Legacy fields for backward compatibility
    prediction: string;
    is_phishing: boolean;
    confidence?: number;
    risk_level?: string;
    
    // Dual-model results
    model_1?: ModelResult;
    model_2?: ModelResult;
    
    // Combined assessment
    combined_assessment?: {
      is_phishing: boolean;
      confidence: number;
      risk_level: string;
      consensus: boolean;
      explanation: string;
    };
    
    // Analysis metadata
    analysis_timestamp: string;
    models_used: string[];
  }> {
    const analysisTimestamp = new Date().toISOString();
    const modelsUsed: string[] = [];
    
    // Run both models in parallel for better performance
    const [model1Result, model2Result] = await Promise.allSettled([
      this.runModel1Analysis(message, authHeaders),
      this.runModel2Analysis(message)
    ]);

    // Process Model 1 results
    let model1Data: ModelResult | undefined;
    if (model1Result.status === "fulfilled") {
      model1Data = model1Result.value;
      modelsUsed.push("BERT-Enhanced");
      console.log("✅ Model 1 (BERT-Enhanced) analysis completed");
    } else {
      console.error("❌ Model 1 (BERT-Enhanced) analysis failed:", model1Result.reason);
    }

    // Process Model 2 results
    let model2Data: ModelResult | undefined;
    if (model2Result.status === "fulfilled") {
      model2Data = model2Result.value;
      modelsUsed.push("Gemini-1.5-Flash");
      console.log("✅ Model 2 (Gemini) analysis completed");
    } else {
      console.error("❌ Model 2 (Gemini) analysis failed:", model2Result.reason);
    }

    // Generate combined assessment
    const combinedAssessment = this.generateCombinedAssessment(model1Data, model2Data);

    // Determine legacy fields for backward compatibility
    const primaryModel = model1Data || model2Data;
    const legacyPrediction = combinedAssessment.is_phishing ? "phishing" : "ham";
    
    return {
      // Legacy fields
      prediction: legacyPrediction,
      is_phishing: combinedAssessment.is_phishing,
      confidence: combinedAssessment.confidence,
      risk_level: combinedAssessment.risk_level,
      
      // Dual-model results
      model_1: model1Data,
      model_2: model2Data,
      
      // Combined assessment
      combined_assessment: combinedAssessment,
      
      // Metadata
      analysis_timestamp: analysisTimestamp,
      models_used: modelsUsed
    };
  }

  /**
   * Run Model 1 (BERT/Enhanced) analysis
   */
  private static async runModel1Analysis(
    message: string,
    authHeaders: Record<string, string>
  ): Promise<ModelResult> {
    try {
      const response = await axios.post(
        `${AI_SERVICE_URL}/sms/detect-phishing`,
        { message },
        { headers: authHeaders }
      );

      const data = response.data;
      
      return {
        is_phishing: data.is_phishing || false,
        confidence: data.confidence || 0.0,
        risk_level: data.risk_level || "low",
        model_name: "BERT-Enhanced",
        explanation: data.explanation || "BERT-based transformer model analysis",
        status: "success"
      };
    } catch (error: any) {
      throw new Error(`Model 1 analysis failed: ${error.message}`);
    }
  }

  /**
   * Run Model 2 (Gemini API) analysis
   */
  private static async runModel2Analysis(message: string): Promise<ModelResult> {
    if (!geminiService.isAvailable()) {
      throw new Error("Gemini API service is not available");
    }

    return await geminiService.analyzeSMS(message);
  }

  /**
   * Generate combined assessment from both model results
   */
  private static generateCombinedAssessment(
    model1?: ModelResult,
    model2?: ModelResult
  ): {
    is_phishing: boolean;
    confidence: number;
    risk_level: string;
    consensus: boolean;
    explanation: string;
  } {
    // If only one model succeeded, use its result
    if (model1 && !model2) {
      return {
        is_phishing: model1.is_phishing,
        confidence: model1.confidence,
        risk_level: model1.risk_level,
        consensus: false,
        explanation: `Analysis based on Model 1 (${model1.model_name}) only. ${model1.explanation || ""}`
      };
    }

    if (model2 && !model1) {
      return {
        is_phishing: model2.is_phishing,
        confidence: model2.confidence,
        risk_level: model2.risk_level,
        consensus: false,
        explanation: `Analysis based on Model 2 (${model2.model_name}) only. ${model2.explanation || ""}`
      };
    }

    // If both models failed, return safe defaults
    if (!model1 && !model2) {
      return {
        is_phishing: false,
        confidence: 0.0,
        risk_level: "low",
        consensus: false,
        explanation: "Both models failed to analyze the message. Defaulting to safe classification."
      };
    }

    // Both models succeeded - combine their results
    if (model1 && model2) {
      const consensus = model1.is_phishing === model2.is_phishing;
      
      // Calculate weighted average confidence (Model 1 gets slightly higher weight as it's proven)
      const combinedConfidence = (model1.confidence * 0.6) + (model2.confidence * 0.4);
      
      // Determine final classification
      let finalIsPhishing: boolean;
      let finalRiskLevel: string;
      let explanation: string;

      if (consensus) {
        // Both models agree
        finalIsPhishing = model1.is_phishing;
        finalRiskLevel = this.getHigherRiskLevel(model1.risk_level, model2.risk_level);
        explanation = `Both models agree: ${finalIsPhishing ? 'PHISHING' : 'SAFE'}. Model 1 confidence: ${(model1.confidence * 100).toFixed(1)}%, Model 2 confidence: ${(model2.confidence * 100).toFixed(1)}%`;
      } else {
        // Models disagree - use higher confidence or default to safer option
        if (model1.confidence > model2.confidence) {
          finalIsPhishing = model1.is_phishing;
          finalRiskLevel = model1.risk_level;
          explanation = `Models disagree. Using Model 1 result (higher confidence: ${(model1.confidence * 100).toFixed(1)}% vs ${(model2.confidence * 100).toFixed(1)}%)`;
        } else if (model2.confidence > model1.confidence) {
          finalIsPhishing = model2.is_phishing;
          finalRiskLevel = model2.risk_level;
          explanation = `Models disagree. Using Model 2 result (higher confidence: ${(model2.confidence * 100).toFixed(1)}% vs ${(model1.confidence * 100).toFixed(1)}%)`;
        } else {
          // Equal confidence - default to safer option (not phishing)
          finalIsPhishing = false;
          finalRiskLevel = "medium";
          explanation = `Models disagree with equal confidence. Defaulting to safer classification (not phishing)`;
        }
      }

      return {
        is_phishing: finalIsPhishing,
        confidence: combinedConfidence,
        risk_level: finalRiskLevel,
        consensus,
        explanation
      };
    }

    // Fallback (should not reach here)
    return {
      is_phishing: false,
      confidence: 0.0,
      risk_level: "low",
      consensus: false,
      explanation: "Unexpected error in combined assessment"
    };
  }

  /**
   * Get the higher risk level between two risk levels
   */
  private static getHigherRiskLevel(risk1: string, risk2: string): string {
    const riskOrder = { "low": 1, "medium": 2, "high": 3 };
    const level1 = riskOrder[risk1 as keyof typeof riskOrder] || 1;
    const level2 = riskOrder[risk2 as keyof typeof riskOrder] || 1;
    
    const higherLevel = Math.max(level1, level2);
    return Object.keys(riskOrder).find(key => riskOrder[key as keyof typeof riskOrder] === higherLevel) || "low";
  }
}
