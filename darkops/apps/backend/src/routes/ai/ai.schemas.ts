import { z } from "zod";

// Common Schemas
export const ErrorResponseSchema = z.object({
  message: z.string(),
});

// URL Analysis Schemas
export const URLAnalysisRequestSchema = z.object({
  url: z.string().url(),
});

export const URLAnalysisResponseSchema = z.object({
  url: z.string(),
  is_safe: z.boolean(),
  confidence: z.number(),
  prediction: z.string(),
});

export const URLFullAnalysisResponseSchema = z.object({
  phishing_analysis: URLAnalysisResponseSchema,
  url_analysis: z.record(z.any()),
});

// SMS Analysis Schemas
export const SMSAnalysisRequestSchema = z.object({
  message: z.string().describe("SMS message text to analyze"),
});

// Model result schema for individual model responses
export const ModelResultSchema = z.object({
  is_phishing: z.boolean(),
  confidence: z.number(),
  risk_level: z.string(),
  model_name: z.string(),
  explanation: z.string().optional(),
  status: z.string(),
});

// Updated SMS Analysis Response Schema for dual-model system
export const SMSAnalysisResponseSchema = z.object({
  // Legacy fields for backward compatibility
  prediction: z.string(),
  is_phishing: z.boolean(),
  confidence: z.number().optional(),
  risk_level: z.string().optional(),

  // Dual-model results
  model_1: ModelResultSchema.optional(), // BERT/Enhanced model
  model_2: ModelResultSchema.optional(), // Gemini API model

  // Combined assessment
  combined_assessment: z
    .object({
      is_phishing: z.boolean(),
      confidence: z.number(),
      risk_level: z.string(),
      consensus: z.boolean(), // Whether both models agree
      explanation: z.string(),
    })
    .optional(),

  // Analysis metadata
  analysis_timestamp: z.string().optional(),
  models_used: z.array(z.string()).optional(),
});

// APK Analysis Schemas
export const APKAnalysisResponseSchema = z.object({
  static_analysis: z.record(z.any()),
  security_analysis: z.record(z.any()),
  virustotal_analysis: z.record(z.any()),
});

export const MalwareDetectionResponseSchema = z.object({
  is_malware: z.boolean(),
  confidence: z.number(),
  threats_detected: z.array(z.string()),
  scan_results: z.record(z.any()),
});

export const APKFullAnalysisResponseSchema = z.object({
  static_analysis: z.record(z.any()),
  security_analysis: z.record(z.any()),
});

// Email Analysis Schemas
export const EmailAnalysisResponseSchema = z.object({
  headers: z.record(z.any()),
  sender_ip: z.string(),
  ip_info: z.record(z.any()),
  attachments: z.array(
    z.object({
      filename: z.string(),
      path: z.string(),
    })
  ),
  phishing_detection: z.object({
    prediction: z.string().nullable(),
  }),
  analysis_timestamp: z.string(),
});

export const EmailScreenshotResponseSchema = z.object({
  screenshot_path: z.string(),
  timestamp: z.string(),
});

export const EmailAttachmentScanResponseSchema = z.object({
  filename: z.string(),
  scan_id: z.string(),
  scan_results: z.record(z.any()),
  threats_detected: z.array(z.string()),
  scan_timestamp: z.string(),
});

// Type exports
export type URLAnalysisRequest = z.infer<typeof URLAnalysisRequestSchema>;
export type URLAnalysisResponse = z.infer<typeof URLAnalysisResponseSchema>;
export type URLFullAnalysisResponse = z.infer<
  typeof URLFullAnalysisResponseSchema
>;
export type SMSAnalysisRequest = z.infer<typeof SMSAnalysisRequestSchema>;
export type SMSAnalysisResponse = z.infer<typeof SMSAnalysisResponseSchema>;
export type ModelResult = z.infer<typeof ModelResultSchema>;
export type APKAnalysisResponse = z.infer<typeof APKAnalysisResponseSchema>;
export type MalwareDetectionResponse = z.infer<
  typeof MalwareDetectionResponseSchema
>;
export type APKFullAnalysisResponse = z.infer<
  typeof APKFullAnalysisResponseSchema
>;
// QR Analysis Schemas
export const QRAnalysisRequestSchema = z.object({
  content: z.string().describe("QR code content to analyze"),
});

export const QRAnalysisResponseSchema = z.object({
  content: z.string(),
  content_type: z.string(),
  is_url: z.boolean(),
  url_analysis: z.record(z.any()).optional(),
  analysis_timestamp: z.string(),
});

export type EmailAnalysisResponse = z.infer<typeof EmailAnalysisResponseSchema>;
export type EmailScreenshotResponse = z.infer<
  typeof EmailScreenshotResponseSchema
>;
export type EmailAttachmentScanResponse = z.infer<
  typeof EmailAttachmentScanResponseSchema
>;
export type QRAnalysisRequest = z.infer<typeof QRAnalysisRequestSchema>;
export type QRAnalysisResponse = z.infer<typeof QRAnalysisResponseSchema>;
