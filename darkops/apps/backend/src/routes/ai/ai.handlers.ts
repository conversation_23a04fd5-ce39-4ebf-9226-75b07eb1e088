import type { Handler, Context } from "hono";
import type { AppBindings } from "@/lib/types.js";
import axios from "axios";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { AITrackingService } from "@/services/aiTracking.service.ts";
import { SmsAnalysisService } from "@/services/smsAnalysis.service.ts";
import jwt from "jsonwebtoken";
import env from "@/env.ts";

// Use the environment variable for the AI service URL
const AI_SERVICE_URL = env.AI_SERVICE_URL;

// Helper function to extract and validate user ID
const getUserId = (c: Context): string | null => {
  const token = c.req.header("Authorization")?.split(" ")[1];
  if (!token) return null;
  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as { userId: string };
    return decoded.userId;
  } catch {
    return null;
  }
};

// Helper function to get auth headers for AI service requests
const getAuthHeaders = (
  c: Context,
  contentType: string = "application/json"
) => {
  const authHeader = c.req.header("Authorization");

  // Log the auth header for debugging
  console.log("Forwarding Authorization header:", authHeader);

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    console.warn("Missing or invalid Bearer token in request");
  }

  return {
    "Content-Type": contentType,
    Authorization: authHeader, // Forward the auth token to AI services
  };
};

// APK Analysis Handlers
export const analyzeAPKHandler: Handler<AppBindings> = async (c: Context) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.parseBody();
    const file = body["file"];
    if (!file) {
      return c.json(
        { message: "No file provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const formData = new FormData();
    formData.append("file", file);

    const response = await axios.post(
      `${AI_SERVICE_URL}/apk/analyze`,
      formData,
      {
        headers: getAuthHeaders(c, "multipart/form-data"),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "APK",
      targetName: file instanceof File ? file.name : "unknown.apk",
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "APK",
        targetName: "unknown.apk",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error analyzing APK" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const detectMalwareHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.parseBody();
    const file = body["file"];
    if (!file) {
      return c.json(
        { message: "No file provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const formData = new FormData();
    formData.append("file", file);

    const response = await axios.post(
      `${AI_SERVICE_URL}/apk/detect-malware`,
      formData,
      {
        headers: getAuthHeaders(c, "multipart/form-data"),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "APK",
      targetName: file instanceof File ? file.name : "unknown.apk",
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "APK",
        targetName: "unknown.apk",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error detecting malware" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const fullAnalysisAPKHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.parseBody();
    const file = body["file"];
    if (!file) {
      return c.json(
        { message: "No file provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const formData = new FormData();
    formData.append("file", file);

    // Log the file name for debugging
    const fileName = file instanceof File ? file.name : "unknown.apk";
    console.log(`DEBUG - APK file name: ${fileName}`);

    const response = await axios.post(
      `${AI_SERVICE_URL}/apk/full-analysis`,
      formData,
      {
        headers: getAuthHeaders(c, "multipart/form-data"),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "APK",
      targetName: file instanceof File ? file.name : "unknown.apk",
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "APK",
        targetName: "unknown.apk",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      {
        message:
          error.response?.data?.detail || "Error performing full analysis",
      },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

// URL Analysis Handlers
export const scanUrlHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const { url } = await c.req.json();
    if (!url) {
      return c.json(
        { message: "URL is required" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    try {
      const response = await axios.post(
        `${AI_SERVICE_URL}/url/scan-url`,
        {
          url,
        },
        {
          headers: getAuthHeaders(c),
        }
      );

      // Track scan with enhanced data
      await AITrackingService.trackScan(userId, {
        scanType: "URL",
        targetName: url,
        result: response.data,
        status: "SUCCESS",
      });

      return c.json(response.data);
    } catch (apiError: any) {
      // Track failed scan
      await AITrackingService.trackScan(userId, {
        scanType: "URL",
        targetName: url,
        result: apiError.response?.data || { error: "Unknown error" },
        status: "FAILED",
      });

      return c.json(
        { message: apiError.response?.data?.detail || "Error scanning URL" },
        apiError.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
      );
    }
  } catch (error: any) {
    console.error("Unexpected error in scanUrlHandler:", error);
    return c.json(
      { message: "An unexpected error occurred" },
      HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const analyzeUrlHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const { url } = await c.req.json();
    const response = await axios.post(
      `${AI_SERVICE_URL}/url/analyze-url`,
      {
        url,
      },
      {
        headers: getAuthHeaders(c),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "URL",
      targetName: url,
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "URL",
        targetName: "unknown_url",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error analyzing URL" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const fullAnalysisUrlHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const { url } = await c.req.json();
    const response = await axios.post(
      `${AI_SERVICE_URL}/url/full-analysis`,
      {
        url,
      },
      {
        headers: getAuthHeaders(c),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "URL",
      targetName: url,
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "URL",
        targetName: "unknown_url",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      {
        message:
          error.response?.data?.detail || "Error performing full URL analysis",
      },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

// Email Analysis Handlers
export const detectEmailPhishingHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.parseBody();
    const file = body["email_file"];
    if (!file) {
      return c.json(
        { message: "No file provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const formData = new FormData();
    formData.append("email_file", file);

    const response = await axios.post(
      `${AI_SERVICE_URL}/email/detect-phishing`,
      formData,
      {
        headers: getAuthHeaders(c, "multipart/form-data"),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "EMAIL",
      targetName: file instanceof File ? file.name : "unknown.eml",
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "EMAIL",
        targetName: "unknown.eml",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error analyzing email" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const analyzeEmailHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.parseBody();
    const file = body["email_file"];
    if (!file) {
      return c.json(
        { message: "No file provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const formData = new FormData();
    formData.append("email_file", file);

    const response = await axios.post(
      `${AI_SERVICE_URL}/email/analyze`,
      formData,
      {
        headers: getAuthHeaders(c, "multipart/form-data"),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "EMAIL",
      targetName: file instanceof File ? file.name : "unknown.eml",
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "EMAIL",
        targetName: "unknown.eml",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error analyzing email" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const scanAttachmentHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.parseBody();
    const file = body["file"];
    if (!file) {
      return c.json(
        { message: "No file provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    const formData = new FormData();
    formData.append("file", file);

    const response = await axios.post(
      `${AI_SERVICE_URL}/email/scan-attachment`,
      formData,
      {
        headers: getAuthHeaders(c, "multipart/form-data"),
      }
    );

    await AITrackingService.trackScan(userId, {
      scanType: "EMAIL",
      targetName: file instanceof File ? file.name : "unknown_attachment",
      result: response.data,
      status: "SUCCESS",
    });

    return c.json(response.data);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "EMAIL",
        targetName: "unknown_attachment",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error scanning attachment" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

export const captureScreenshotHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.parseBody();
    const file = body["email_file"];

    if (!file || !(file instanceof File)) {
      return c.json(
        { message: "No file provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    console.log("Received file for screenshot:", file.name);

    const formData = new FormData();
    formData.append("email_file", file);

    console.log("Sending request to AI service for screenshot capture");

    const response = await axios.post(
      `${AI_SERVICE_URL}/email/capture-screenshot`,
      formData,
      {
        headers: getAuthHeaders(c, "multipart/form-data"),
        responseType: "arraybuffer",
      }
    );

    console.log("Received response from AI service");

    await AITrackingService.trackScan(userId, {
      scanType: "EMAIL",
      targetName: file.name,
      result: { screenshot: true },
      status: "SUCCESS",
    });

    c.header("Content-Type", "image/jpeg");
    c.header("Content-Disposition", 'inline; filename="screenshot.jpg"');
    return c.body(response.data);
  } catch (error: any) {
    console.error("Error in captureScreenshotHandler:", error);
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "EMAIL",
        targetName: "screenshot",
        result: error.response?.data,
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error capturing screenshot" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

// SMS Analysis Handler
export const detectSMSPhishingHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const body = await c.req.json();
    if (!body.message) {
      return c.json(
        { message: "No message provided" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    console.log("🔍 Starting dual-model SMS analysis...");

    // Use the new dual-model analysis service
    const { DualModelSMSService } = await import(
      "@/services/dualModelSMS.service.js"
    );
    const analysisResult = await DualModelSMSService.analyzeSMS(
      body.message,
      getAuthHeaders(c)
    );

    console.log("📊 Dual-model SMS Analysis completed:", {
      models_used: analysisResult.models_used,
      consensus: analysisResult.combined_assessment?.consensus,
      final_result: analysisResult.is_phishing ? "PHISHING" : "SAFE",
    });

    // Extract scan engines for compatibility with existing GraphQL schema
    const scanEngines = SmsAnalysisService.extractScanEngines(analysisResult);
    console.log(
      "Extracted Scan Engines:",
      JSON.stringify(scanEngines, null, 2)
    );

    await AITrackingService.trackScan(userId, {
      scanType: "SMS",
      targetName: body.message.substring(0, 50) + "...", // Store first 50 chars of SMS
      result: analysisResult,
      status: "SUCCESS",
    });

    return c.json(analysisResult);
  } catch (error: any) {
    console.error("❌ SMS analysis failed:", error);
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "SMS",
        targetName: "unknown_message",
        result: error.response?.data || { error: error.message },
        status: "FAILED",
      });
    }
    return c.json(
      {
        message:
          error.response?.data?.detail ||
          error.message ||
          "Error analyzing SMS",
      },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};

// QR Analysis Handler
export const analyzeQRHandler: Handler<AppBindings> = async (c) => {
  try {
    const userId = getUserId(c);
    if (!userId) {
      return c.json({ message: "Unauthorized" }, HttpStatusCodes.UNAUTHORIZED);
    }

    const { content } = await c.req.json();
    if (!content) {
      return c.json(
        { message: "QR content is required" },
        HttpStatusCodes.BAD_REQUEST
      );
    }

    // Determine if the content is a URL
    let isUrl = false;
    let urlAnalysisResult = null;

    try {
      // Check if content is a URL
      new URL(content);
      isUrl = true;

      // If it's a URL, use the URL analysis service
      const urlResponse = await axios.post(
        `${AI_SERVICE_URL}/url/full-analysis`,
        { url: content },
        { headers: getAuthHeaders(c) }
      );

      urlAnalysisResult = urlResponse.data;
    } catch (urlError) {
      // Not a URL or URL analysis failed
      console.log(
        "QR content is not a URL or URL analysis failed:",
        urlError.message
      );
    }

    // Prepare the QR analysis result
    const qrAnalysisResult = {
      content: content,
      content_type: isUrl ? "URL" : "TEXT",
      is_url: isUrl,
      url_analysis: urlAnalysisResult,
      analysis_timestamp: new Date().toISOString(),
    };

    // Track the QR scan
    await AITrackingService.trackScan(userId, {
      scanType: "QR",
      targetName: content.substring(0, 50) + (content.length > 50 ? "..." : ""),
      result: qrAnalysisResult,
      status: "SUCCESS",
    });

    return c.json(qrAnalysisResult);
  } catch (error: any) {
    const userId = getUserId(c);
    if (userId) {
      await AITrackingService.trackScan(userId, {
        scanType: "QR",
        targetName: "unknown_qr",
        result: error.response?.data || { error: error.message },
        status: "FAILED",
      });
    }
    return c.json(
      { message: error.response?.data?.detail || "Error analyzing QR code" },
      error.response?.status || HttpStatusCodes.INTERNAL_SERVER_ERROR
    );
  }
};
