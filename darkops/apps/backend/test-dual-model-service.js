/**
 * Test script to verify dual-model service integration
 */

import { config } from "dotenv";

// Load environment variables first
config();

async function testDualModelService() {
  console.log("🧪 Testing Dual-Model Service Integration\n");
  
  try {
    // Dynamic import to ensure env is loaded first
    const { DualModelSMSService } = await import("./src/services/dualModelSMS.service.js");
    
    console.log("📦 Dual-model service imported successfully");
    
    // Mock auth headers (similar to what the backend would send)
    const mockAuthHeaders = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token'
    };
    
    // Test with a phishing message
    const testMessage = "URGENT: Your bank account has been compromised. Click here immediately to secure your account: http://fake-bank.com/secure";
    console.log(`\n📱 Testing with message: "${testMessage}"`);
    
    console.log("\n🚀 Starting dual-model analysis...");
    const result = await DualModelSMSService.analyzeSMS(testMessage, mockAuthHeaders);
    
    console.log("\n📊 Dual-Model Analysis Results:");
    console.log("=".repeat(50));
    
    // Legacy fields
    console.log("📋 Legacy Fields:");
    console.log("   prediction:", result.prediction);
    console.log("   is_phishing:", result.is_phishing);
    console.log("   confidence:", result.confidence);
    console.log("   risk_level:", result.risk_level);
    
    // Model results
    console.log("\n🤖 Model Results:");
    if (result.model_1) {
      console.log("   Model 1 (BERT-Enhanced):");
      console.log("     is_phishing:", result.model_1.is_phishing);
      console.log("     confidence:", result.model_1.confidence);
      console.log("     risk_level:", result.model_1.risk_level);
      console.log("     status:", result.model_1.status);
    } else {
      console.log("   Model 1: NOT AVAILABLE");
    }
    
    if (result.model_2) {
      console.log("   Model 2 (Gemini):");
      console.log("     is_phishing:", result.model_2.is_phishing);
      console.log("     confidence:", result.model_2.confidence);
      console.log("     risk_level:", result.model_2.risk_level);
      console.log("     status:", result.model_2.status);
      console.log("     explanation:", result.model_2.explanation);
    } else {
      console.log("   Model 2: NOT AVAILABLE");
    }
    
    // Combined assessment
    console.log("\n🎯 Combined Assessment:");
    if (result.combined_assessment) {
      console.log("   is_phishing:", result.combined_assessment.is_phishing);
      console.log("   confidence:", result.combined_assessment.confidence);
      console.log("   risk_level:", result.combined_assessment.risk_level);
      console.log("   consensus:", result.combined_assessment.consensus);
      console.log("   explanation:", result.combined_assessment.explanation);
    } else {
      console.log("   NOT AVAILABLE");
    }
    
    // Metadata
    console.log("\n📈 Metadata:");
    console.log("   models_used:", result.models_used);
    console.log("   analysis_timestamp:", result.analysis_timestamp);
    
    console.log("\n🎉 Dual-model service test completed successfully!");
    
    // Verify expected behavior
    console.log("\n✅ Verification:");
    if (result.model_1 && result.model_2) {
      console.log("   ✅ Both models executed successfully");
    } else if (result.model_1 && !result.model_2) {
      console.log("   ⚠️  Only Model 1 executed (Model 2 failed)");
    } else if (!result.model_1 && result.model_2) {
      console.log("   ⚠️  Only Model 2 executed (Model 1 failed)");
    } else {
      console.log("   ❌ Both models failed");
    }
    
  } catch (error) {
    console.error("❌ Dual-model service test failed:", error);
    console.error("Error type:", error.constructor.name);
    console.error("Error message:", error.message);
    console.error("Error stack:", error.stack);
  }
}

// Run the test
testDualModelService().catch(console.error);
