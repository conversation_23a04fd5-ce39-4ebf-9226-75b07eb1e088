/**
 * Test script to verify AI service connectivity
 */

import axios from "axios";
import { config } from "dotenv";

// Load environment variables
config();

async function testAIService() {
  console.log("🧪 Testing AI Service Connectivity\n");
  
  const AI_SERVICE_URL = process.env.AI_SERVICE_URL || "http://0.0.0.0:8000";
  console.log("🔗 AI Service URL:", AI_SERVICE_URL);
  
  try {
    // Test basic connectivity
    console.log("\n🔄 Testing basic connectivity...");
    const healthResponse = await axios.get(`${AI_SERVICE_URL}/`, {
      timeout: 5000
    });
    
    console.log("✅ AI service is accessible");
    console.log("📊 Health check response:", healthResponse.data);
    
    // Test SMS analysis endpoint
    console.log("\n🔄 Testing SMS analysis endpoint...");
    const testMessage = "URGENT: Your bank account has been compromised. Click here to secure it: http://fake-bank.com";
    
    const smsResponse = await axios.post(
      `${AI_SERVICE_URL}/sms/detect-phishing`,
      { message: testMessage },
      {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log("✅ SMS analysis endpoint is working");
    console.log("📊 SMS analysis response:", smsResponse.data);
    
    console.log("\n🎉 AI service test completed successfully!");
    
  } catch (error) {
    console.error("❌ AI service test failed:", error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log("\n💡 Connection refused. Possible issues:");
      console.log("   1. AI service is not running");
      console.log("   2. AI service is running on a different port");
      console.log("   3. Firewall is blocking the connection");
      console.log("\n🔧 To start the AI service:");
      console.log("   cd ai-service");
      console.log("   python -m uvicorn main:app --host 0.0.0.0 --port 8000");
    } else if (error.code === 'ETIMEDOUT') {
      console.log("\n💡 Connection timeout. Possible issues:");
      console.log("   1. AI service is slow to respond");
      console.log("   2. Network connectivity issues");
      console.log("   3. AI service is overloaded");
    } else if (error.response) {
      console.log("\n💡 HTTP Error:", error.response.status, error.response.statusText);
      console.log("   Response data:", error.response.data);
    }
  }
}

// Run the test
testAIService().catch(console.error);
