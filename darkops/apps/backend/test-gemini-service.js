/**
 * Test script to verify Gemini service integration within backend context
 */

import { config } from "dotenv";

// Load environment variables first
config();

// Import the Gemini service
async function testGeminiService() {
  console.log("🧪 Testing Gemini Service Integration\n");
  
  try {
    // Dynamic import to ensure env is loaded first
    const { geminiService } = await import("./src/services/gemini.service.js");
    
    console.log("📦 Gemini service imported successfully");
    console.log("🔧 Service available:", geminiService.isAvailable());
    
    if (!geminiService.isAvailable()) {
      console.log("❌ Gemini service is not available");
      console.log("   This could be due to:");
      console.log("   1. Missing or invalid API key");
      console.log("   2. Service initialization failure");
      console.log("   3. Import/module issues");
      return;
    }
    
    console.log("✅ Gemini service is available");
    
    // Test connection
    console.log("\n🔄 Testing Gemini service connection...");
    const connectionTest = await geminiService.testConnection();
    console.log("🔗 Connection test result:", connectionTest);
    
    if (!connectionTest) {
      console.log("❌ Connection test failed");
      return;
    }
    
    // Test SMS analysis
    const testMessage = "URGENT: Your bank account has been compromised. Click here to secure it: http://fake-bank.com";
    console.log(`\n📱 Testing SMS analysis with: "${testMessage}"`);
    
    const result = await geminiService.analyzeSMS(testMessage);
    console.log("\n📊 Analysis result:");
    console.log("   is_phishing:", result.is_phishing);
    console.log("   confidence:", result.confidence);
    console.log("   risk_level:", result.risk_level);
    console.log("   model_name:", result.model_name);
    console.log("   status:", result.status);
    console.log("   explanation:", result.explanation);
    
    console.log("\n🎉 Gemini service test completed successfully!");
    
  } catch (error) {
    console.error("❌ Gemini service test failed:", error);
    console.error("Error type:", error.constructor.name);
    console.error("Error message:", error.message);
    console.error("Error stack:", error.stack);
  }
}

// Run the test
testGeminiService().catch(console.error);
