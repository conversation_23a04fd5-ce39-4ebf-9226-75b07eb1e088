/**
 * Test script for dual-model SMS phishing detection
 * This script tests the integration between Model 1 (BERT) and Model 2 (Gemini)
 */

import { DualModelSMSService } from "./src/services/dualModelSMS.service.js";
import { geminiService } from "./src/services/gemini.service.js";

// Test messages
const testMessages = [
  {
    name: "Safe Message",
    text: "Hello, how are you doing today? Hope you're having a great day!"
  },
  {
    name: "Potential Phishing",
    text: "URGENT: Your bank account has been compromised. Click here immediately to secure your account: http://fake-bank.com/secure"
  },
  {
    name: "Suspicious Link",
    text: "Congratulations! You've won $1000. Claim your prize now: bit.ly/claim-prize"
  }
];

async function testDualModelSystem() {
  console.log("🧪 Testing Dual-Model SMS Phishing Detection System\n");
  
  // Check Gemini service availability
  console.log("🔍 Checking Gemini API availability...");
  const geminiAvailable = geminiService.isAvailable();
  console.log(`Gemini API Status: ${geminiAvailable ? '✅ Available' : '❌ Not Available'}\n`);
  
  if (!geminiAvailable) {
    console.log("⚠️  Gemini API not available. Testing will use Model 1 only.\n");
  }

  // Mock auth headers for testing
  const mockAuthHeaders = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer test-token'
  };

  for (const testMessage of testMessages) {
    console.log(`\n📱 Testing: ${testMessage.name}`);
    console.log(`Message: "${testMessage.text}"`);
    console.log("─".repeat(80));
    
    try {
      const result = await DualModelSMSService.analyzeSMS(
        testMessage.text,
        mockAuthHeaders
      );
      
      console.log("📊 Analysis Results:");
      console.log(`├─ Final Classification: ${result.is_phishing ? '🚨 PHISHING' : '✅ SAFE'}`);
      console.log(`├─ Overall Confidence: ${(result.confidence * 100).toFixed(1)}%`);
      console.log(`├─ Risk Level: ${result.risk_level?.toUpperCase()}`);
      console.log(`├─ Models Used: ${result.models_used.join(', ')}`);
      
      if (result.model_1) {
        console.log(`├─ Model 1 (${result.model_1.model_name}):`);
        console.log(`│  ├─ Classification: ${result.model_1.is_phishing ? 'PHISHING' : 'SAFE'}`);
        console.log(`│  ├─ Confidence: ${(result.model_1.confidence * 100).toFixed(1)}%`);
        console.log(`│  └─ Risk Level: ${result.model_1.risk_level}`);
      }
      
      if (result.model_2) {
        console.log(`├─ Model 2 (${result.model_2.model_name}):`);
        console.log(`│  ├─ Classification: ${result.model_2.is_phishing ? 'PHISHING' : 'SAFE'}`);
        console.log(`│  ├─ Confidence: ${(result.model_2.confidence * 100).toFixed(1)}%`);
        console.log(`│  └─ Risk Level: ${result.model_2.risk_level}`);
      }
      
      if (result.combined_assessment) {
        console.log(`└─ Combined Assessment:`);
        console.log(`   ├─ Consensus: ${result.combined_assessment.consensus ? '✅ YES' : '❌ NO'}`);
        console.log(`   └─ Explanation: ${result.combined_assessment.explanation}`);
      }
      
    } catch (error) {
      console.error(`❌ Error analyzing message: ${error.message}`);
    }
  }
  
  console.log("\n🎉 Dual-model testing completed!");
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDualModelSystem().catch(console.error);
}

export { testDualModelSystem };
