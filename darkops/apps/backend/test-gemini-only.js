/**
 * Simple test script to verify Gemini API integration
 */

import { config } from "dotenv";
import { GoogleGenerativeAI } from "@google/generative-ai";

// Load environment variables
config();

async function testGeminiAPI() {
  console.log("🧪 Testing Gemini API Integration\n");
  
  // Check environment variable
  const apiKey = process.env.GEMINI_API_KEY;
  console.log("🔑 API Key present:", !!apiKey);
  console.log("🔑 API Key length:", apiKey?.length || 0);
  
  if (!apiKey) {
    console.log("❌ GEMINI_API_KEY not found in environment variables");
    return;
  }
  
  if (apiKey.length < 10) {
    console.log("❌ API Key appears to be invalid (too short)");
    return;
  }
  
  try {
    console.log("\n🚀 Initializing Gemini API client...");
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    console.log("✅ Gemini API client initialized successfully");
    
    // Test with a simple message
    const testMessage = "Hello, this is a test message for phishing detection.";
    console.log(`\n📱 Testing with message: "${testMessage}"`);
    
    const prompt = `
You are an expert cybersecurity analyst specializing in SMS phishing detection. Analyze the following SMS message and determine if it's a phishing attempt.

SMS Message: "${testMessage}"

Please analyze this message for phishing indicators such as:
- Urgent language or threats
- Requests for personal information
- Suspicious links or URLs
- Impersonation of legitimate organizations
- Grammar and spelling errors
- Unusual sender behavior
- Financial requests or offers

Respond with a JSON object in this exact format:
{
  "is_phishing": boolean,
  "confidence": number (0.0 to 1.0),
  "risk_level": "low" | "medium" | "high",
  "explanation": "Brief explanation of your analysis and key indicators found"
}

Be precise and only respond with the JSON object, no additional text.
`;

    console.log("🔄 Sending request to Gemini API...");
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log("📡 Received response from Gemini API");
    console.log("📄 Raw response:", text);
    
    // Try to parse the JSON response
    try {
      let jsonText = text.trim();
      
      // Remove markdown code blocks if present
      if (jsonText.includes("```json")) {
        const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonText = jsonMatch[1];
        }
      } else if (jsonText.includes("```")) {
        const jsonMatch = jsonText.match(/```\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          jsonText = jsonMatch[1];
        }
      }
      
      const parsed = JSON.parse(jsonText);
      console.log("\n✅ Successfully parsed JSON response:");
      console.log("   is_phishing:", parsed.is_phishing);
      console.log("   confidence:", parsed.confidence);
      console.log("   risk_level:", parsed.risk_level);
      console.log("   explanation:", parsed.explanation);
      
      console.log("\n🎉 Gemini API test completed successfully!");
      
    } catch (parseError) {
      console.error("❌ Failed to parse JSON response:", parseError);
      console.log("Raw response text:", text);
    }
    
  } catch (error) {
    console.error("❌ Gemini API test failed:", error);
    console.error("Error type:", error.constructor.name);
    console.error("Error message:", error.message);
    
    if (error.message.includes("API_KEY_INVALID")) {
      console.log("\n💡 The API key appears to be invalid. Please check:");
      console.log("   1. The API key is correct");
      console.log("   2. The API key has the necessary permissions");
      console.log("   3. The Gemini API is enabled for your project");
    }
  }
}

// Run the test
testGeminiAPI().catch(console.error);
