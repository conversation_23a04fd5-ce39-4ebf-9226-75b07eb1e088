/**
 * Test script to verify the backend API is running the updated dual-model code
 */

import axios from "axios";
import { config } from "dotenv";

// Load environment variables
config();

async function testBackendAPI() {
  console.log("🧪 Testing Backend API with Dual-Model SMS Analysis\n");
  
  const BACKEND_URL = "http://localhost:9999";
  console.log("🔗 Backend URL:", BACKEND_URL);
  
  try {
    // Test basic connectivity
    console.log("🔄 Testing backend connectivity...");
    const healthResponse = await axios.get(`${BACKEND_URL}/health`, {
      timeout: 5000
    });
    
    console.log("✅ Backend is accessible");
    console.log("📊 Health check response:", healthResponse.data);
    
  } catch (healthError) {
    console.error("❌ Backend health check failed:", healthError.message);
    
    if (healthError.code === 'ECONNREFUSED') {
      console.log("\n💡 Backend is not running. To start it:");
      console.log("   cd darkops/apps/backend");
      console.log("   npx tsx src/index.ts");
      return;
    }
  }
  
  try {
    // Test SMS analysis endpoint with authentication
    console.log("\n🔄 Testing SMS analysis endpoint...");
    
    // You'll need to replace this with a valid JWT token
    // For testing, you might need to login first or use a test token
    const testToken = "your-jwt-token-here"; // Replace with actual token
    
    const testMessage = "URGENT: Your bank account has been compromised. Click here to secure it: http://fake-bank.com";
    console.log("📱 Test message:", testMessage);
    
    const smsResponse = await axios.post(
      `${BACKEND_URL}/ai/sms/detect-phishing`,
      { message: testMessage },
      {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testToken}`
        }
      }
    );
    
    console.log("✅ SMS analysis endpoint is working");
    console.log("📊 SMS analysis response:");
    console.log(JSON.stringify(smsResponse.data, null, 2));
    
    // Check if dual-model data is present
    const response = smsResponse.data;
    console.log("\n🔍 Dual-Model Analysis Check:");
    
    if (response.model_1) {
      console.log("✅ Model 1 (BERT-Enhanced) data present");
      console.log("   is_phishing:", response.model_1.is_phishing);
      console.log("   confidence:", response.model_1.confidence);
      console.log("   model_name:", response.model_1.model_name);
    } else {
      console.log("❌ Model 1 data missing");
    }
    
    if (response.model_2) {
      console.log("✅ Model 2 (Gemini) data present");
      console.log("   is_phishing:", response.model_2.is_phishing);
      console.log("   confidence:", response.model_2.confidence);
      console.log("   model_name:", response.model_2.model_name);
    } else {
      console.log("❌ Model 2 data missing");
    }
    
    if (response.combined_assessment) {
      console.log("✅ Combined assessment present");
      console.log("   consensus:", response.combined_assessment.consensus);
      console.log("   explanation:", response.combined_assessment.explanation);
    } else {
      console.log("❌ Combined assessment missing");
    }
    
    console.log("\n📈 Models used:", response.models_used);
    
    if (response.models_used && response.models_used.length === 2) {
      console.log("🎉 Dual-model system is working correctly!");
    } else if (response.models_used && response.models_used.length === 1) {
      console.log("⚠️  Only one model is working. Check the logs for errors.");
    } else {
      console.log("❌ No models detected in response.");
    }
    
  } catch (error) {
    console.error("❌ SMS analysis test failed:", error.message);
    
    if (error.response) {
      console.log("HTTP Status:", error.response.status);
      console.log("Response data:", error.response.data);
      
      if (error.response.status === 401) {
        console.log("\n💡 Authentication failed. You need a valid JWT token.");
        console.log("   1. Login to the application first");
        console.log("   2. Extract the JWT token from the browser");
        console.log("   3. Update the testToken variable in this script");
      }
    }
  }
}

// Run the test
testBackendAPI().catch(console.error);
