// lib/dashboard/sms_analyzer.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:permission_handler/permission_handler.dart';
import '../blocs/scan/scan_bloc.dart';
import '../blocs/scan/scan_event.dart';
import '../blocs/scan/scan_state.dart';
import '../models/scan/scan_models.dart';

class SMSAnalyzerPage extends StatefulWidget {
  const SMSAnalyzerPage({super.key});

  @override
  State<SMSAnalyzerPage> createState() => _SMSAnalyzerPageState();
}

class _SMSAnalyzerPageState extends State<SMSAnalyzerPage> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _analyzeSMS() {
    final text = _controller.text.trim();
    if (text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter an SMS message'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    context.read<ScanBloc>().add(AnalyzeSMSEvent(text));
  }

  void _readDeviceSMS() async {
    final permission = await Permission.sms.status;
    if (permission.isDenied) {
      final result = await Permission.sms.request();
      if (result.isDenied && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('SMS permission is required to read messages'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    if (mounted) {
      context.read<ScanBloc>().add(const ParseSMSFromDeviceEvent());
    }
  }

  void _resetState() {
    _controller.clear();
    context.read<ScanBloc>().add(const ClearScanResultEvent());
  }

  @override
  Widget build(BuildContext context) {
    final Color primaryBackgroundColor =
        Theme.of(context).scaffoldBackgroundColor;
    final Color cardBackgroundColor = Theme.of(context).cardColor;
    const Color primaryBlue = Color.fromARGB(255, 139, 92, 246);
    final Color primaryTextColor =
        Theme.of(context).textTheme.bodyMedium?.color ?? Colors.white;

    return Scaffold(
      backgroundColor: primaryBackgroundColor,
      appBar: AppBar(
        backgroundColor: primaryBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new, color: primaryTextColor),
          tooltip: 'Back',
          onPressed: () => Navigator.pop(context),
        ),
        title: _buildHeader(primaryTextColor),
        titleSpacing: 0,
      ),
      body: BlocConsumer<ScanBloc, ScanState>(
        listener: (context, state) {
          if (state.hasError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Analysis failed'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 32),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 800),
                  child: Column(
                    children: [
                      // Input Section
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: cardBackgroundColor,
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInputOptions(primaryBlue, primaryTextColor),
                            const SizedBox(height: 16),
                            _buildInputSection(primaryBlue, state),
                          ],
                        ),
                      ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3),

                      const SizedBox(height: 24),

                      // Results Section
                      if (state.isLoading)
                        _buildLoadingSection(
                          primaryBlue,
                          primaryTextColor,
                        ).animate().fadeIn(duration: 400.ms),

                      if (state.hasResult &&
                          state.currentScanType == ScanType.sms)
                        _buildResultSection(
                          state.smsResult!,
                          primaryBlue,
                          primaryTextColor,
                          cardBackgroundColor,
                        ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(Color textColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: const Color.fromARGB(
              255,
              139,
              92,
              246,
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: const Icon(
            Icons.sms_outlined,
            color: Color.fromARGB(255, 139, 92, 246),
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Text(
          'SMS Analysis',
          style: TextStyle(
            color: textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildInputOptions(Color primaryBlue, Color textColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose SMS Input Method:',
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // Focus on text input
                },
                icon: const Icon(Icons.edit_outlined, size: 20),
                label: const Text('Manual Input'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryBlue.withValues(alpha: 0.1),
                  foregroundColor: primaryBlue,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _readDeviceSMS,
                icon: const Icon(Icons.smartphone_outlined, size: 20),
                label: const Text('Read from Device'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryBlue.withValues(alpha: 0.1),
                  foregroundColor: primaryBlue,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInputSection(Color primaryBlue, ScanState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter SMS message below:',
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyMedium?.color,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          color: Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _controller,
              maxLines: 6,
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
              decoration: InputDecoration(
                hintText: 'Paste the SMS message to analyze...',
                hintStyle: TextStyle(
                  color: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                ),
                border: InputBorder.none,
              ),
              enabled: !state.isLoading,
            ),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: state.isLoading ? null : _analyzeSMS,
            icon:
                state.isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                    : const Icon(Icons.security_outlined),
            label: Text(
              state.isLoading ? 'Analyzing...' : 'Analyze SMS',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryBlue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
              ),
              elevation: 0,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingSection(Color primaryBlue, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: primaryBlue.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          CircularProgressIndicator(color: primaryBlue, strokeWidth: 3),
          const SizedBox(height: 16),
          Text(
            'Analyzing SMS for threats...',
            style: TextStyle(
              color: textColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This may take a few seconds',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultSection(
    SMSAnalysisResponse result,
    Color primaryBlue,
    Color textColor,
    Color cardColor,
  ) {
    // Use primary result (combined assessment if available, otherwise legacy)
    final isPhishing = result.primaryIsPhishing;
    final threatColor = isPhishing ? Colors.red : Colors.green;
    final threatIcon =
        isPhishing ? Icons.warning_rounded : Icons.check_circle_rounded;
    final threatText = isPhishing ? 'PHISHING DETECTED' : 'SAFE MESSAGE';

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: threatColor.withValues(alpha: 0.3), width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Threat Level Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: threatColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(threatIcon, color: threatColor, size: 28),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      threatText,
                      style: TextStyle(
                        color: threatColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      result.prediction,
                      style: TextStyle(
                        color: textColor.withValues(alpha: 0.8),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Combined Assessment (if available)
          if (result.combinedAssessment != null) ...[
            _buildCombinedAssessmentSection(result, textColor, primaryBlue),
            const SizedBox(height: 20),
          ],

          // Individual Model Results (if available)
          if (result.isDualModel) ...[
            _buildDualModelSection(result, textColor, primaryBlue),
            const SizedBox(height: 20),
          ],

          // Legacy Analysis Details (fallback)
          if (!result.isDualModel && result.combinedAssessment == null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: textColor.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Analysis Details',
                    style: TextStyle(
                      color: textColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  if (result.confidence != null)
                    _buildDetailRow(
                      'Confidence',
                      _formatConfidence(result.confidence!),
                      textColor,
                    ),
                  _buildDetailRow(
                    'Threat Level',
                    result.primaryRiskLevel.toUpperCase(),
                    textColor,
                  ),
                  _buildDetailRow(
                    'Analysis Time',
                    result.analysisTimestamp ?? 'Just now',
                    textColor,
                  ),
                ],
              ),
            ),

          const SizedBox(height: 24),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _resetState,
                  icon: const Icon(Icons.refresh_rounded),
                  label: const Text('Analyze Another'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryBlue.withValues(alpha: 0.1),
                    foregroundColor: primaryBlue,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Share or save result
                  },
                  icon: const Icon(Icons.share_rounded),
                  label: const Text('Share Result'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryBlue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Formats confidence value properly handling both decimal and percentage formats
  String _formatConfidence(double confidence) {
    // Check if confidence is already in percentage format (greater than 1)
    if (confidence > 1) {
      return '${confidence.toStringAsFixed(1)}%';
    }
    // Otherwise, convert from decimal to percentage
    return '${(confidence * 100).toStringAsFixed(1)}%';
  }

  Widget _buildCombinedAssessmentSection(
    SMSAnalysisResponse result,
    Color textColor,
    Color primaryBlue,
  ) {
    final assessment = result.combinedAssessment!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: textColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: primaryBlue.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                assessment.consensus ? Icons.check_circle : Icons.warning,
                color: assessment.consensus ? Colors.green : Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Combined Assessment',
                style: TextStyle(
                  color: textColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color:
                      assessment.consensus
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  assessment.consensus ? 'Models Agree' : 'Models Disagree',
                  style: TextStyle(
                    color: assessment.consensus ? Colors.green : Colors.orange,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildDetailRow(
            'Final Confidence',
            _formatConfidence(assessment.confidence),
            textColor,
          ),
          _buildDetailRow(
            'Risk Level',
            assessment.riskLevel.toUpperCase(),
            textColor,
          ),
          if (result.modelsUsed != null)
            _buildDetailRow(
              'Models Used',
              '${result.modelsUsed!.length}',
              textColor,
            ),
          if (assessment.explanation.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: primaryBlue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                assessment.explanation,
                style: TextStyle(
                  color: textColor.withValues(alpha: 0.8),
                  fontSize: 13,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDualModelSection(
    SMSAnalysisResponse result,
    Color textColor,
    Color primaryBlue,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Individual Model Results',
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            if (result.hasModel1)
              Expanded(
                child: _buildModelCard(
                  result.model1!,
                  result.displayModelName1,
                  textColor,
                  primaryBlue,
                ),
              ),
            if (result.hasModel1 && result.hasModel2) const SizedBox(width: 12),
            if (result.hasModel2)
              Expanded(
                child: _buildModelCard(
                  result.model2!,
                  result.displayModelName2,
                  textColor,
                  primaryBlue,
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildModelCard(
    ModelResult model,
    String displayName,
    Color textColor,
    Color primaryBlue,
  ) {
    final isPhishing = model.isPhishing;
    final statusColor = isPhishing ? Colors.red : Colors.green;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: textColor.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: statusColor.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  isPhishing ? Icons.warning : Icons.check_circle,
                  color: statusColor,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  displayName,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            isPhishing ? 'Phishing' : 'Safe',
            style: TextStyle(
              color: statusColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Confidence: ${_formatConfidence(model.confidence)}',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 11,
            ),
          ),
          Text(
            'Risk: ${model.riskLevel.toUpperCase()}',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 11,
            ),
          ),
          if (model.explanation != null && model.explanation!.isNotEmpty) ...[
            const SizedBox(height: 6),
            Text(
              model.explanation!,
              style: TextStyle(
                color: textColor.withValues(alpha: 0.6),
                fontSize: 10,
                fontStyle: FontStyle.italic,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}
